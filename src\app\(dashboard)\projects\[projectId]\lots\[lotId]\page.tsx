import { <PERSON>Header, PageTitle, PageDescription } from '@/components/layout/page-header'
import { PageContent } from '@/components/layout/page-content'
import { getLot } from '@/features/projects/api'
import { LotTables } from '@/features/projects/components/lot-tables'

export default async function LotPage({ params }: { params: Promise<{ projectId: string; lotId: string }> }) {
  const { projectId, lotId } = await params
  const lot = await getLot(lotId)

  return (
    <PageContent>
      <PageHeader>
        <div>
          <PageTitle>Lot: {lot?.name || lotId}</PageTitle>
          <PageDescription>Informations du lot & documents associés</PageDescription>
        </div>
        <div />
      </PageHeader>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="rounded-md border p-4">
          <div className="text-sm text-muted-foreground">Lot ID</div>
          <div className="text-lg font-medium">{lot?.id}</div>
        </div>
        <div className="rounded-md border p-4">
          <div className="text-sm text-muted-foreground">Projet</div>
          <div className="text-lg font-medium">{projectId}</div>
        </div>
        <div className="rounded-md border p-4">
          <div className="text-sm text-muted-foreground">Infos</div>
          <div className="text-lg font-medium">—</div>
        </div>
      </div>

      <LotTables lotId={lotId} />
    </PageContent>
  )
}

