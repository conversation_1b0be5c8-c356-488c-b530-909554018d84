import Link from 'next/link'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title, PageDescription } from '@/components/layout/page-header'
import { PageContent } from '@/components/layout/page-content'
import { ProjectLotsTable } from '@/features/projects/components/project-lots-table'
import { getProject } from '@/features/projects/api'
import { ProjectDetails } from '@/features/projects/components/project-details'

export default async function ProjectPage({ params }: { params: Promise<{ projectId: string }> }) {
  const { projectId } = await params
  const project = await getProject(projectId)

  return (
    <PageContent>
      <PageHeader>
        <div>
          <PageTitle>Projet: {project?.name || projectId}</PageTitle>
          <PageDescription>Informations rapides & lots associés</PageDescription>
        </div>
        <div />
      </PageHeader>

      {project && <ProjectDetails project={project} />}

      <div className="mt-6">
        <h2 className="text-xl font-semibold mb-2">Lots</h2>
        <ProjectLotsTable projectId={projectId} />
        <div className="mt-2 text-sm text-muted-foreground">
          <Link className="underline" href={`/projects/${projectId}/lots`}>Voir tous les lots</Link>
        </div>
      </div>
    </PageContent>
  )
}

