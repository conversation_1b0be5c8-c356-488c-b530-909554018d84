# 🚀 ORBIS Frontend - Plan de Développement

## 🎨 Palette de Couleurs
- **<PERSON><PERSON> Principal**: `#1DD1A1` (boutons, liens actifs)
- **<PERSON><PERSON>**: `#025959` (sidebar, navigation)
- **Teal <PERSON><PERSON>**: `#014040` (texte principal, bordures)
- **<PERSON><PERSON>**: `#03A696` (hover, états actifs)
- **<PERSON><PERSON>**: `#F2F2F2` (arrière-plans)

## 📋 Checklist de Développement

### ✅ Phase 1: Configuration de Base
- [x] ~~Créer projet Next.js 14 + TypeScript + Tailwind~~
- [x] ~~Installer et configurer Shadcn/ui~~
- [x] ~~Configurer la palette de couleurs personnalisée~~
- [x] ~~Configurer les types TypeScript pour l'API backend~~
- [x] ~~Installer les dépendances supplémentaires (React Query, Zustand, etc.)~~

### 🏗️ Phase 2: Architecture & Layout
- [x] ~~Créer la structure de dossiers modulaire~~
- [x] ~~Développer le composant Layout principal~~
- [x] ~~C<PERSON>er la Sidebar avec navigation~~
- [x] ~~Développer le Header avec gestion utilisateur~~
- [x] ~~Implémenter le système de routing~~

### 🧩 Phase 3: Composants Réutilisables
- [ ] Créer le composant DataTable universel
- [ ] Développer les composants de formulaires
- [ ] Créer les composants de filtres
- [ ] Implémenter les modales CRUD
- [ ] Développer les composants de statut/badges

### 📊 Phase 4: Pages Principales
- [ ] Page Dashboard (accès rapide aux derniers éléments)
- [ ] Page Liste des Projets
- [ ] Page Détail Projet
- [ ] Page Liste des Lots (dans un projet)
- [ ] Page Détail Lot (avec 3 tableaux)

### 🔌 Phase 5: Intégration API
- [ ] Configurer les services API
- [ ] Implémenter l'authentification
- [ ] Connecter les endpoints CRUD
- [ ] Gérer les états de chargement
- [ ] Implémenter la gestion d'erreurs

### 📱 Phase 6: Responsive & Optimisation
- [ ] Optimiser pour mobile/tablette
- [ ] Tester la performance
- [ ] Optimiser les bundles
- [ ] Tests end-to-end

---

## 📁 Structure de Dossiers Prévue

```
src/
├── app/                    # App Router Next.js
│   ├── (dashboard)/       # Route group pour pages principales
│   ├── globals.css        # Styles globaux
│   ├── layout.tsx         # Layout racine
│   └── page.tsx           # Page d'accueil
├── components/            # Composants réutilisables
│   ├── ui/               # Composants Shadcn/ui
│   ├── layout/           # Layout components
│   ├── forms/            # Composants de formulaires
│   ├── tables/           # Composant DataTable
│   └── common/           # Composants communs
├── lib/                  # Utilitaires et configuration
│   ├── api.ts           # Configuration API
│   ├── auth.ts          # Gestion authentification
│   ├── utils.ts         # Utilitaires
│   └── types.ts         # Types TypeScript
├── hooks/               # Hooks personnalisés
├── stores/              # Gestion d'état (Zustand)
└── styles/              # Styles supplémentaires
```

## 🗃️ Modèles de Données (Backend)

### Project
- `id`, `name`, `description`, `status`, `nature`
- `start_date`, `end_date`, `budget`
- `address`, `city`, `postal_code`, `country`
- Relations: `lots[]`, `technical_documents[]`

### Lot
- `id`, `project_id`, `name`, `description`, `phase`
- `order_number`, `budget`, `start_date`, `end_date`
- Relations: `stakeholders[]`, `technical_documents[]`

### Stakeholder (Intervenants)
- `id`, `lot_id`, `tcompany_id`, `role`, `description`
- Relations: `lot`, `tcompany`

### TCompany (Entreprises)
- `id`, `name`, `code`, `type`, `siret`
- `address`, `city`, `email`, `phone`, `website`

### TechnicalDocument (Pièces Techniques)
- `id`, `project_id`, `lot_id`, `title`, `content`
- `document_type`, `indice`, `version`

### Document (Pièces Générales)
- `id`, `workspace_id`, `name`, `description`
- `file_path`, `file_size`, `mime_type`

## 🎯 Spécifications Détaillées

### DataTable Component
**Fonctionnalités requises:**
- Colonnes configurables
- Tri sur toutes les colonnes
- Filtres en ligne (au-dessus des colonnes)
- Hover effects sur les lignes
- Actions CRUD intégrées (modifier/supprimer)
- Pagination
- Responsive design

### Pages Spécifiques

#### Dashboard
- Widgets derniers projets modifiés
- Widgets derniers lots modifiés
- Widgets dernières pièces techniques
- Navigation rapide

#### Page Projet
- Détails techniques complets
- Liste des lots associés (DataTable)
- Actions: créer/modifier/supprimer lots

#### Page Lot
- Informations du lot en haut
- 3 tableaux en colonnes:
  1. **Intervenants**: nom entreprise, rôle, contact
  2. **Pièces Générales**: nom, type, taille, date
  3. **Pièces Techniques**: titre, type, indice, version

### Couleurs d'Application
- Sidebar: `#025959`
- Boutons primaires: `#1DD1A1`
- Hover states: `#03A696`
- Texte principal: `#014040`
- Arrière-plans: `#F2F2F2`

---

## 🔄 Prochaines Étapes
1. Configurer Shadcn/ui avec la palette personnalisée
2. Créer la structure de dossiers
3. Développer le layout principal
4. Implémenter le composant DataTable
5. Créer les pages principales


## 🔧 Phase 3.1 — DataTable universel (sélection retenue)

- Choix: TanStack Table + React Query + shadcn/ui (table/inputs)
- Pourquoi (4 lignes):
  - Léger, composable et headless: s’intègre à notre design system shadcn sans surpoids.
  - Server-side prêt (pagination/tri/filtre) avec React Query; logique réutilisable par feature.
  - API ergonomique via ColumnDef<T> + toolbars modulaires; excellent support TS.
  - Alternatives (MUI DataGrid, AntD) plus lourdes et moins alignées avec nos tokens.

### Spécification
- Colonnes: ColumnDef<T> (header, accessor, cell, enableSorting, filterFn, meta { width, align })
- Toolbar en tête:
  - Bouton “Ajouter” à gauche (avant les filtres)
  - Filtres clés (texte, select, date range) mappés aux colonnes importantes
  - Boutons: reset filtres, export CSV (optionnel)
- Actions par ligne: Modifier, Supprimer (avec confirm) + menu “…”
- Pagination/tri/filtre: côté serveur (queryKey = [entity, page, size, sort, filters])
- États: skeleton loading, empty state, error (toast + retry)
- Accessibilité: navigation clavier, rôles ARIA, tailles responsives
- Permissions: masquage des actions selon droits (via AuthContext.permissions)

### API composant (draft)
- <DataTable
    entity="projects" // clé de cache
    columns={columns}
    fetcher={listProjects} // (params) => Promise<Page<Project>>
    onAdd={onAdd}
    onEdit={onEdit}
    onDelete={onDelete}
    initialState={{ pageSize: 20 }}
  />
- Types: Page<T> { items: T[]; total: number; page: number; size: number }
- Filters: { [field: string]: string | number | string[] | [from,to] }

### Tâches à réaliser
- [ ] Core Table (src/components/tables/data-table.tsx)
- [ ] Toolbar: AddButton + FiltersRow (src/components/tables/data-table-toolbar.tsx)
- [ ] Pagination + PageSize (src/components/tables/data-table-pagination.tsx)
- [ ] Hook serveur (src/hooks/use-data-table.ts): mapping state -> query params, cache keys
- [ ] Cellule Actions (src/components/tables/row-actions.tsx) avec confirm + toasts
- [ ] Filtres types: TextFilter, SelectFilter, DateRangeFilter (src/components/tables/filters/*)
- [ ] Exemple “Projects” (features/projects): columns + page liste branchée
- [ ] Exemple “Companies” (features/companies): columns + page liste
- [ ] Export CSV (optionnel) via fetcher et colonnes visibles
- [ ] Tests unit (render, tri/filtre/pagination, actions) + doc “HowTo” rapide

### Conventions
- Pages restent minces: elles importent DataTable + columns depuis features/*
- Back: endpoints acceptent page, size, sort, filters; renvoient Page<T>
- i18n (plus tard): headers/labels externalisés
