"use client";

import React from 'react';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { HeaderActions } from './header-actions';
import { NotificationBell } from './notification-bell';
import { UserMenu } from './user-menu';
import { Breadcrumbs } from './breadcrumbs';

export function Header() {
  return (
    <header className="flex h-16 items-center justify-between border-b border-border bg-background px-6">
      {/* Left side - Sidebar trigger + Breadcrumbs */}
      <div className="flex items-center gap-4 min-w-0">
        <SidebarTrigger className="h-8 w-8" />
        <Separator orientation="vertical" className="h-6" />
        <div className="truncate">
          <Breadcrumbs />
        </div>
      </div>

      {/* Right side - Actions + Notifications + User menu */}
      <div className="flex items-center space-x-2">
        <HeaderActions />
        <NotificationBell count={0} />
        <UserMenu />
      </div>
    </header>
  );
}
