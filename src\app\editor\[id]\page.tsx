"use client";

import React from "react";
import { EditorContent } from "@tiptap/react";
import { useEditorSetup } from "../hooks/use-editor-setup";
import { usePageTracking } from "../hooks/use-page-tracking";
import { usePageHeaders } from "../hooks/use-page-headers";
import { usePageSeparators } from "../hooks/use-page-separators";
import { MainToolbar } from "../components/main-toolbar";
import "../styles/word-editor.css";

/**
 * Editor page avec pagination
 */
export default function EditorPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  // Unwrap Next.js route params with React.use()
  const { id: routeId } = React.use(params);
  const docId = routeId || "demo";

  // Setup editor
  const editor = useEditorSetup(docId);
  const { pages, contentRef, scrollRef } = usePageTracking(editor);

  // Hook pour les en-têtes et pieds de page dynamiques
  const pageHeadersRef = usePageHeaders({
    docId,
    headerLeft: "Document ORBIS",
    headerRight: new Date().toLocaleDateString('fr-FR'),
    footerLeft: "Page {page}",
    footerRight: `Document ${docId}`,
  });

  // Hook pour les séparateurs de page
  const pageSeparatorsRef = usePageSeparators();

  if (!editor) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-lg">Chargement de l'éditeur...</div>
      </div>
    );
  }

  return (
    <div className="flex h-screen w-full flex-col overflow-hidden">
      {/* Toolbar */}
      <MainToolbar editor={editor} />

      {/* Editor area avec pagination CSS simple */}
      <div
        ref={scrollRef}
        className="flex-1 overflow-auto"
        style={{ backgroundColor: '#f0f0f0', padding: '20px' }}
      >
        <div
          ref={(el) => {
            if (contentRef) contentRef.current = el;
            if (pageHeadersRef) pageHeadersRef.current = el;
            if (pageSeparatorsRef) pageSeparatorsRef.current = el;
          }}
          className="word-document-container"
          style={{
            maxWidth: '794px',
            margin: '0 auto',
            backgroundColor: 'white',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            border: '1px solid #d0d0d0',
            minHeight: '1123px',
            padding: '96px',
            position: 'relative'
          }}
        >
          {/* Contenu de l'éditeur */}
          <EditorContent editor={editor} />
        </div>

        {/* Indicateur de pages */}
        <div className="fixed bottom-4 right-4 bg-white/90 backdrop-blur-sm border rounded-lg px-3 py-1 text-sm text-gray-600 shadow-lg">
          {pages} page{pages > 1 ? "s" : ""}
        </div>
      </div>
    </div>
  );
}