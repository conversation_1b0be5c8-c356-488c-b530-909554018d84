import { Node, mergeAttributes } from "@tiptap/core";

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    tableOfContents: {
      setTableOfContents: () => ReturnType;
      updateTableOfContents: () => ReturnType;
    };
  }
}

interface TocEntry {
  id: string;
  text: string;
  level: number;
  page: number;
}

export const TableOfContents = Node.create({
  name: "tableOfContents",
  group: "block",
  atom: true,
  selectable: true,
  draggable: false,

  addAttributes() {
    return {
      entries: {
        default: [],
        parseHTML: element => {
          const data = element.getAttribute("data-entries");
          try {
            return data ? JSON.parse(data) : [];
          } catch (e) {
            console.warn("Failed to parse TOC entries:", e);
            return [];
          }
        },
        renderHTML: attributes => ({
          "data-entries": JSON.stringify(attributes.entries || []),
        }),
      },
    };
  },

  parseHTML() {
    return [{ tag: "div[data-type='table-of-contents']" }];
  },

  renderHTML({ HTMLAttributes }) {
    const entriesData = HTMLAttributes["data-entries"];
    const entries: TocEntry[] = Array.isArray(entriesData) ? entriesData : [];

    const tocItems = entries.length > 0 ? entries.map(entry => {
      const indent = "  ".repeat(entry.level - 1);
      const dots = ".".repeat(Math.max(1, 80 - entry.text.length - indent.length - entry.page.toString().length));
      
      return [
        "div",
        { 
          class: `toc-entry toc-level-${entry.level}`,
          "data-heading-id": entry.id,
        },
        [
          "span",
          { class: "toc-text" },
          `${indent}${entry.text}`,
        ],
        [
          "span",
          { class: "toc-dots" },
          dots,
        ],
        [
          "span",
          { class: "toc-page" },
          `p${entry.page}`,
        ],
      ];
    }) : [];

    return [
      "div",
      mergeAttributes(HTMLAttributes, {
        "data-type": "table-of-contents",
        class: "table-of-contents",
      }),
      [
        "h2",
        { class: "toc-title" },
        "Sommaire",
      ],
      [
        "div",
        { class: "toc-entries" },
        ...tocItems,
      ],
    ];
  },

  addCommands() {
    return {
      setTableOfContents:
        () =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: { entries: [] },
          });
        },

      updateTableOfContents:
        () =>
        ({ editor, tr }) => {
          const { doc } = editor.state;
          const entries: TocEntry[] = [];
          let currentPage = 1;

          // Fonction pour calculer la page d'un noeud
          const getPageForNode = (pos: number): number => {
            // Cette fonction sera améliorée avec l'intégration du système de pagination
            // Pour l'instant, estimation basée sur la position
            const pageHeight = 1000; // Hauteur estimée d'une page en caractères
            return Math.floor(pos / pageHeight) + 1;
          };

          // Parcourir le document pour trouver les headings
          doc.descendants((node, pos) => {
            if (node.type.name === "heading") {
              const level = node.attrs.level;
              const text = node.textContent;
              const id = `heading-${pos}`;
              const page = getPageForNode(pos);

              entries.push({
                id,
                text,
                level,
                page,
              });
            }
          });

          // Mettre à jour tous les noeuds table-of-contents
          doc.descendants((node, pos) => {
            if (node.type.name === "tableOfContents") {
              tr.setNodeMarkup(pos, undefined, { entries });
            }
          });

          return true;
        },
    };
  },

  addKeyboardShortcuts() {
    return {
      "Mod-Shift-T": () => this.editor.commands.setTableOfContents(),
      "Mod-Shift-U": () => this.editor.commands.updateTableOfContents(),
    };
  },

  // Auto-update TOC when content changes
  onCreate() {
    this.editor.on("update", () => {
      // Délai pour laisser le temps à la pagination de se mettre à jour
      setTimeout(() => {
        this.editor.commands.updateTableOfContents();
      }, 500);
    });
  },
});
