'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'

function prettify(segment: string) {
  return segment
    .replace(/-/g, ' ')
    .replace(/\b\w/g, (c) => c.toUpperCase())
}

export function Breadcrumbs() {
  const pathname = usePathname()
  const segments = pathname.split('/').filter(Boolean)
  const paths = segments.map((seg, i) => ({
    name: prettify(seg),
    href: '/' + segments.slice(0, i + 1).join('/'),
  }))

  if (paths.length === 0) return null

  return (
    <nav aria-label="Fil d'Ariane" className="text-sm text-muted-foreground">
      <ol className="flex items-center gap-2">
        <li><Link href="/" className="hover:underline">Accueil</Link></li>
        {paths.map((p, idx) => (
          <li key={p.href} className="flex items-center gap-2">
            <span className="opacity-50">/</span>
            {idx < paths.length - 1 ? (
              <Link href={p.href} className="hover:underline">{p.name}</Link>
            ) : (
              <span className="text-foreground font-medium">{p.name}</span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  )
}

