import React from "react";
import { Editor } from "@tiptap/react";
import { ToolbarButton } from "./toolbar-button";
import { Heading1, Heading2, List, Minus } from "lucide-react";

interface FloatingMenuProps {
  editor: Editor;
  visible: boolean;
  top: number;
  left: number;
}

export function FloatingMenu({ editor, visible, top, left }: FloatingMenuProps) {
  if (!visible) return null;

  return (
    <div
      className="pointer-events-auto absolute z-20 -translate-y-1/2"
      style={{ top, left }}
    >
      <div className="flex items-center gap-1 rounded-md border bg-background p-1 shadow-sm">
        <ToolbarButton
          onClick={() => editor.chain().focus().setParagraph().run()}
          title="Paragraphe"
        >
          P
        </ToolbarButton>
        <ToolbarButton
          onClick={() =>
            editor.chain().focus().toggleHeading({ level: 1 }).run()
          }
          title="Titre 1"
        >
          <Heading1 className="size-4" />
        </ToolbarButton>
        <ToolbarButton
          onClick={() =>
            editor.chain().focus().toggleHeading({ level: 2 }).run()
          }
          title="Titre 2"
        >
          <Heading2 className="size-4" />
        </ToolbarButton>
        <ToolbarButton
          onClick={() =>
            editor.chain().focus().toggleBulletList().run()
          }
          title="Liste à puces"
        >
          <List className="size-4" />
        </ToolbarButton>
      </div>
    </div>
  );
}
