import { Home, FolderOpen, Building2, FileText, Settings } from 'lucide-react';

export const navigationItems = [
  { title: 'Dashboard', href: '/dashboard', icon: Home },
  { title: 'Projets', href: '/projects', icon: FolderOpen },
  { title: 'Entreprises', href: '/companies', icon: Building2 },
  { title: 'Documents', href: '/documents', icon: FileText },
  { title: 'Paramètres', href: '/settings', icon: Settings },
] as const;

export type NavigationItem = (typeof navigationItems)[number];

