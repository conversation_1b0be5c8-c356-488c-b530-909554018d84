import { ColumnDef } from '@/components/tables/types'
import type { Stakeholder, GDocument, TDocument } from './mocks'

export const stakeholderColumns: ColumnDef<Stakeholder>[] = [
  { id: 'company', header: 'Entreprise', accessor: (r) => r.company },
  { id: 'role', header: 'Rô<PERSON>', accessor: (r) => r.role },
  { id: 'contact', header: 'Contact', accessor: (r) => r.contact },
]

export const gdocColumns: ColumnDef<GDocument>[] = [
  { id: 'name', header: 'Nom', accessor: (r) => r.name },
  { id: 'type', header: 'Type', accessor: (r) => r.type },
  { id: 'size', header: 'Taille', accessor: (r) => r.size },
  { id: 'date', header: 'Date', accessor: (r) => r.date },
]

export const tdocColumns: ColumnDef<TDocument>[] = [
  { id: 'title', header: 'Titre', accessor: (r) => r.title },
  { id: 'doc_type', header: 'Type', accessor: (r) => r.doc_type },
  { id: 'indice', header: 'Indice', accessor: (r) => r.indice },
  { id: 'version', header: 'Version', accessor: (r) => r.version },
]

