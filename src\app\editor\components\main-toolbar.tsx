import React from "react";
import { Editor } from "@tiptap/react";
import { ToolbarButton } from "./toolbar-button";
import {
  Bold,
  Italic,
  Heading1,
  Heading2,
  List,
  ListOrdered,
  Underline,
} from "lucide-react";

interface MainToolbarProps {
  editor: Editor | null;
}

export function MainToolbar({ editor }: MainToolbarProps) {
  if (!editor) return null;

  const isActive = (nameOrAttrs: any, attrs?: any) =>
    !!editor?.isActive(nameOrAttrs as any, attrs as any);
  const can = editor?.can();

  return (
    <div className="sticky top-0 z-10 w-full border-b bg-background/80 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="w-full px-4 py-2 flex items-center gap-2 overflow-x-auto">
        <ToolbarButton
          onClick={() => editor?.chain().focus().toggleBold().run()}
          active={isActive("bold")}
          disabled={!can?.chain().focus().toggleBold().run()}
          title="Gras (Ctrl/Cmd+B)"
        >
          <Bold className="size-4" />
          <span className="hidden sm:inline">Gras</span>
        </ToolbarButton>

        <ToolbarButton
          onClick={() => editor?.chain().focus().toggleItalic().run()}
          active={isActive("italic")}
          disabled={!can?.chain().focus().toggleItalic().run()}
          title="Italique (Ctrl/Cmd+I)"
        >
          <Italic className="size-4" />
          <span className="hidden sm:inline">Italique</span>
        </ToolbarButton>

        <div className="mx-1 h-5 w-px bg-border" />

        <ToolbarButton
          onClick={() =>
            editor?.chain().focus().toggleHeading({ level: 1 }).run()
          }
          active={isActive("heading", { level: 1 })}
          disabled={!can?.chain().focus().toggleHeading({ level: 1 }).run()}
          title="Titre 1"
        >
          <Heading1 className="size-4" />
          <span className="hidden sm:inline">H1</span>
        </ToolbarButton>

        <ToolbarButton
          onClick={() =>
            editor?.chain().focus().toggleHeading({ level: 2 }).run()
          }
          active={isActive("heading", { level: 2 })}
          disabled={!can?.chain().focus().toggleHeading({ level: 2 }).run()}
          title="Titre 2"
        >
          <Heading2 className="size-4" />
          <span className="hidden sm:inline">H2</span>
        </ToolbarButton>

        <div className="mx-1 h-5 w-px bg-border" />

        <ToolbarButton
          onClick={() => editor?.chain().focus().toggleBulletList().run()}
          active={isActive("bulletList")}
          disabled={!can?.chain().focus().toggleBulletList().run()}
          title="Liste à puces"
        >
          <List className="size-4" />
          <span className="hidden sm:inline">Puces</span>
        </ToolbarButton>

        <ToolbarButton
          onClick={() => editor?.chain().focus().toggleOrderedList().run()}
          active={isActive("orderedList")}
          disabled={!can?.chain().focus().toggleOrderedList().run()}
          title="Liste numérotée"
        >
          <ListOrdered className="size-4" />
          <span className="hidden sm:inline">Numérotée</span>
        </ToolbarButton>
      </div>
    </div>
  );
}
