'use client';

import { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react';
import { authService } from '@/lib/auth';
import type { AuthState, LoginCredentials } from '@/lib/types';

interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | null>(null);

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    token: null,
    isLoading: true,
    workspace: null,
    permissions: []
  });

  const [isClient, setIsClient] = useState(false);

  // Solution hydratation : attendre le côté client
  useEffect(() => {
    setIsClient(true);
  }, []);

  const login = useCallback(async (credentials: LoginCredentials) => {
    try {
      console.log('🔐 Début de la connexion avec:', credentials.email);
      const response = await authService.login(credentials);
      console.log('✅ Réponse de authService.login:', response);

      setAuthState({
        user: response.user,
        token: response.token,
        isLoading: false,
        workspace: response.workspace,
        permissions: response.permissions
      });
      console.log('✅ AuthState mis à jour');

      // Synchroniser localStorage ET cookies pour le middleware
      if (typeof window !== 'undefined') {
        localStorage.setItem('auth_token', response.token);
        // Définir un cookie pour le middleware
        document.cookie = `auth_token=${response.token}; path=/; max-age=86400; samesite=lax`;
        console.log('✅ Token sauvegardé dans localStorage et cookies');
      }

      return { success: true };
    } catch (error) {
      console.error('❌ Erreur lors de la connexion:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erreur de connexion'
      };
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      await authService.logout();
      setAuthState({
        user: null,
        token: null,
        isLoading: false,
        workspace: null,
        permissions: []
      });

      if (typeof window !== 'undefined') {
        localStorage.removeItem('auth_token');
        // Supprimer le cookie
        document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      }
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    }
  }, []);

  // Initialisation côté client uniquement
  useEffect(() => {
    if (!isClient) return;

    const initAuth = async () => {
      console.log('🔄 Initialisation de l\'authentification...');

      const token = localStorage.getItem('auth_token');

      if (token) {
        try {
          console.log('🎫 Token trouvé, vérification...');
          // Vérifier la validité du token
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/auth/me`, {
            headers: { Authorization: `Bearer ${token}` }
          });

          if (response.ok) {
            const userData = await response.json();
            console.log('✅ Token valide, utilisateur récupéré');

            // Récupérer les workspaces
            const workspacesResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/v1/auth/workspaces`, {
              headers: { Authorization: `Bearer ${token}` }
            });

            let workspaces = [];
            if (workspacesResponse.ok) {
              workspaces = await workspacesResponse.json();
            }

            setAuthState({
              user: userData,
              token,
              isLoading: false,
              workspace: workspaces[0]?.workspace || null,
              permissions: workspaces[0]?.role ? [workspaces[0].role] : []
            });

            // Synchroniser le cookie
            document.cookie = `auth_token=${token}; path=/; max-age=86400; samesite=lax`;
          } else {
            console.log('❌ Token invalide, suppression');
            localStorage.removeItem('auth_token');
            document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
            setAuthState(prev => ({ ...prev, isLoading: false }));
          }
        } catch (error) {
          console.error('❌ Erreur lors de la vérification du token:', error);
          localStorage.removeItem('auth_token');
          document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
          setAuthState(prev => ({ ...prev, isLoading: false }));
        }
      } else {
        console.log('ℹ️ Aucun token trouvé');
        setAuthState(prev => ({ ...prev, isLoading: false }));
      }
    };

    initAuth();
  }, [isClient]);

  const contextValue: AuthContextType = {
    ...authState,
    login,
    logout
  };

  // Rendu côté serveur : état de chargement
  if (!isClient) {
    return (
      <AuthContext.Provider value={{
        user: null,
        token: null,
        isLoading: true,
        workspace: null,
        permissions: [],
        login,
        logout
      }}>
        {children}
      </AuthContext.Provider>
    );
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}
