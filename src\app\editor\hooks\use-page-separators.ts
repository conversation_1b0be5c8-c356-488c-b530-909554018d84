import { useEffect, useRef } from 'react';

export function usePageSeparators() {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    const pageHeight = 1123; // Hauteur A4
    const pageGap = 20; // Espacement entre pages
    
    // Fonction pour ajouter les séparateurs de page
    const addPageSeparators = () => {
      // Supprimer les anciens séparateurs
      const oldSeparators = container.querySelectorAll('.page-separator');
      oldSeparators.forEach(el => el.remove());

      const contentHeight = container.scrollHeight;
      const pageCount = Math.ceil(contentHeight / pageHeight);

      for (let page = 1; page < pageCount; page++) {
        const separatorTop = page * pageHeight;

        // Créer le séparateur
        const separator = document.createElement('div');
        separator.className = 'page-separator';
        separator.style.cssText = `
          position: absolute;
          top: ${separatorTop}px;
          left: -20px;
          right: -20px;
          height: ${pageGap}px;
          background: #f0f0f0;
          z-index: 5;
          pointer-events: none;
        `;

        container.appendChild(separator);
      }
    };

    // Observer pour détecter les changements de contenu
    const observer = new MutationObserver(() => {
      setTimeout(addPageSeparators, 100);
    });

    observer.observe(container, {
      childList: true,
      subtree: true,
      characterData: true
    });

    // Observer pour détecter les changements de taille
    const resizeObserver = new ResizeObserver(() => {
      setTimeout(addPageSeparators, 100);
    });

    resizeObserver.observe(container);

    // Ajouter les séparateurs initiaux
    setTimeout(addPageSeparators, 100);

    return () => {
      observer.disconnect();
      resizeObserver.disconnect();
    };
  }, []);

  return containerRef;
}
