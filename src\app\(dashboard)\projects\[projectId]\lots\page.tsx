import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title, PageDescription } from '@/components/layout/page-header'
import { PageContent } from '@/components/layout/page-content'
import { ProjectLotsTable } from '@/features/projects/components/project-lots-table'

export default function ProjectLotsPage({ params }: { params: { projectId: string } }) {
  return (
    <PageContent>
      <PageHeader>
        <div>
          <PageTitle>Lots</PageTitle>
          <PageDescription>Lots du projet {params.projectId}</PageDescription>
        </div>
        <div />
      </PageHeader>

      <ProjectLotsTable projectId={params.projectId} />
    </PageContent>
  )
}

