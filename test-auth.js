#!/usr/bin/env node
/**
 * Test d'authentification Frontend pour ORBIS
 * Teste la <NAME_EMAIL> / orbis123!
 */

const axios = require('axios');
const fs = require('fs').promises;

// Configuration
const BASE_URL = process.env.API_URL || 'http://localhost:8000';
const API_PREFIX = '/api/v1';
const AUTH_ENDPOINT = `${BASE_URL}${API_PREFIX}/auth`;

// Configuration Supabase
const SUPABASE_URL = 'https://rbfcnmrzpqbnpdyoyzts.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJiZmNubXJ6cHFibnBkeW95enRzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0NjY5NzksImV4cCI6MjA3MDA0Mjk3OX0.py8YU6M41n3MVa3cycZd2WF8iVjGv_F9aXQNlDKnI-U';

class FrontendAuthTester {
    constructor() {
        this.client = axios.create({
            baseURL: BASE_URL,
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json',
            }
        });
        this.accessToken = null;
        this.refreshToken = null;
    }

    async initializeSupabase() {
        console.log('🔧 Initialisation Supabase...');
        this.supabaseAuthUrl = `${SUPABASE_URL}/auth/v1`;
        this.supabaseHeaders = {
            'apikey': SUPABASE_ANON_KEY,
            'Content-Type': 'application/json',
        };
    }

    async testSupabaseLogin(email, password) {
        console.log(`\n🔐 Test connexion Supabase avec ${email}`);
        
        const url = `${this.supabaseAuthUrl}/token?grant_type=password`;
        const payload = {
            email,
            password
        };

        try {
            const response = await axios.post(url, payload, {
                headers: this.supabaseHeaders
            });

            console.log(`📡 POST ${url}`);
            console.log(`📊 Status: ${response.status}`);

            if (response.status === 200) {
                const data = response.data;
                this.accessToken = data.access_token;
                this.refreshToken = data.refresh_token;

                console.log(`✅ Connexion Supabase réussie!`);
                console.log(`🎫 Access Token: ${this.accessToken.substring(0, 50)}...`);
                console.log(`🔄 Refresh Token: ${this.refreshToken.substring(0, 50)}...`);
                console.log(`⏰ Expires in: ${data.expires_in}s`);

                return {
                    success: true,
                    data,
                    tokens: {
                        access: this.accessToken,
                        refresh: this.refreshToken
                    }
                };
            } else {
                console.log(`❌ Erreur Supabase: ${response.status}`);
                return {
                    success: false,
                    error: response.data
                };
            }

        } catch (error) {
            console.log(`❌ Exception Supabase: ${error.response?.data?.message || error.message}`);
            return {
                success: false,
                error: error.response?.data || error.message
            };
        }
    }

    async testBackendLogin(email, password) {
        console.log(`\n🔐 Test connexion Backend ORBIS avec ${email}`);
        
        const url = `${AUTH_ENDPOINT}/login`;
        const payload = {
            email,
            password
        };

        try {
            const response = await this.client.post(url, payload);

            console.log(`📡 POST ${url}`);
            console.log(`📊 Status: ${response.status}`);

            if (response.status === 200) {
                const data = response.data;
                this.accessToken = data.access_token;
                this.refreshToken = data.refresh_token;

                console.log(`✅ Connexion Backend réussie!`);
                console.log(`🎫 Access Token: ${this.accessToken.substring(0, 50)}...`);
                console.log(`🔄 Refresh Token: ${this.refreshToken.substring(0, 50)}...`);
                console.log(`⏰ Expires in: ${data.expires_in}s`);

                return {
                    success: true,
                    data,
                    tokens: {
                        access: this.accessToken,
                        refresh: this.refreshToken
                    }
                };
            } else {
                console.log(`❌ Erreur Backend: ${response.status}`);
                return {
                    success: false,
                    error: response.data
                };
            }

        } catch (error) {
            console.log(`❌ Exception Backend: ${error.response?.data?.detail || error.message}`);
            return {
                success: false,
                error: error.response?.data || error.message
            };
        }
    }

    async testGetCurrentUser() {
        if (!this.accessToken) {
            return { success: false, error: "Pas de token disponible" };
        }

        console.log(`\n👤 Test récupération infos utilisateur`);

        const url = `${AUTH_ENDPOINT}/me`;
        const headers = {
            'Authorization': `Bearer ${this.accessToken}`
        };

        try {
            const response = await this.client.get(url, { headers });

            console.log(`📡 GET ${url}`);
            console.log(`📊 Status: ${response.status}`);

            if (response.status === 200) {
                const userData = response.data;
                console.log(`✅ Infos utilisateur récupérées:`);
                console.log(`   ID: ${userData.id}`);
                console.log(`   Email: ${userData.email}`);
                console.log(`   Nom: ${userData.full_name}`);

                return {
                    success: true,
                    data: userData
                };
            } else {
                console.log(`❌ Erreur: ${response.status}`);
                return {
                    success: false,
                    error: response.data
                };
            }

        } catch (error) {
            console.log(`❌ Exception: ${error.response?.data?.detail || error.message}`);
            return {
                success: false,
                error: error.response?.data || error.message
            };
        }
    }

    async testGetWorkspaces() {
        if (!this.accessToken) {
            return { success: false, error: "Pas de token disponible" };
        }

        console.log(`\n🏢 Test récupération workspaces`);

        const url = `${AUTH_ENDPOINT}/workspaces`;
        const headers = {
            'Authorization': `Bearer ${this.accessToken}`
        };

        try {
            const response = await this.client.get(url, { headers });

            console.log(`📡 GET ${url}`);
            console.log(`📊 Status: ${response.status}`);

            if (response.status === 200) {
                const workspaces = response.data;
                console.log(`✅ Workspaces récupérés: ${workspaces.length}`);

                workspaces.forEach((ws, idx) => {
                    const workspace = ws.workspace || ws;
                    const role = ws.role || 'N/A';
                    console.log(`   ${idx + 1}. ${workspace.name} (Rôle: ${role})`);
                });

                return {
                    success: true,
                    data: workspaces
                };
            } else {
                console.log(`❌ Erreur: ${response.status}`);
                return {
                    success: false,
                    error: response.data
                };
            }

        } catch (error) {
            console.log(`❌ Exception: ${error.response?.data?.detail || error.message}`);
            return {
                success: false,
                error: error.response?.data || error.message
            };
        }
    }

    async testRefreshToken() {
        if (!this.refreshToken) {
            return { success: false, error: "Pas de refresh token disponible" };
        }

        console.log(`\n🔄 Test rafraîchissement token`);

        const url = `${AUTH_ENDPOINT}/refresh`;
        const payload = {
            refresh_token: this.refreshToken
        };

        try {
            const response = await this.client.post(url, payload);

            console.log(`📡 POST ${url}`);
            console.log(`📊 Status: ${response.status}`);

            if (response.status === 200) {
                const data = response.data;
                this.accessToken = data.access_token;
                this.refreshToken = data.refresh_token;

                console.log(`✅ Token rafraîchi!`);
                console.log(`