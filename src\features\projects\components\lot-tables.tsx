'use client'

import { DataTable } from '@/components/tables/data-table'
import { stakeholderColumns, gdocColumns, tdocColumns } from '@/features/projects/lot-columns'
import { listStakeholders, listGDocs, listTDocs } from '@/features/projects/api'

export function LotTables({ lotId }: { lotId: string }) {
  return (
    <div className="mt-6 grid grid-cols-1 xl:grid-cols-3 gap-6">
      <div>
        <h2 className="text-lg font-semibold mb-2">Intervenants</h2>
        <DataTable entity="stakeholders" columns={stakeholderColumns} fetcher={({ page, size }) => listStakeholders(lotId, { page, size })} visibleRows={10} />
      </div>
      <div>
        <h2 className="text-lg font-semibold mb-2">Pièces Générales</h2>
        <DataTable entity="gdocs" columns={gdocColumns} fetcher={({ page, size }) => listGDocs(lotId, { page, size })} visibleRows={10} />
      </div>
      <div>
        <h2 className="text-lg font-semibold mb-2">Pièces Techniques</h2>
        <DataTable entity="tdocs" columns={tdocColumns} fetcher={({ page, size }) => listTDocs(lotId, { page, size })} visibleRows={10} />
      </div>
    </div>
  )
}

