import { useEffect, useState } from "react";
import { Editor } from "@tiptap/react";

interface HeadingInfo {
  id: string;
  text: string;
  level: number;
  position: number;
  number: string; // Numérotation hiérarchique (ex: "1.2.3.a")
}

export function useHeadingNumbering(editor: Editor | null) {
  const [headings, setHeadings] = useState<HeadingInfo[]>([]);

  useEffect(() => {
    if (!editor) return;

    const updateHeadingNumbering = () => {
      try {
        const { doc } = editor.state;
        const newHeadings: HeadingInfo[] = [];
        
        // Compteurs pour chaque niveau
        const counters = {
          h1: 0,
          h2: 0,
          h3: 0,
          h4: 0,
          h5: 0,
        };

        // Lettres pour H4 et H5
        const letters = 'abcdefghijklmnopqrstuvwxyz';

        // Parcourir le document pour trouver les headings
        doc.descendants((node: any, pos: number) => {
          if (node.type.name === "heading") {
            const level = node.attrs.level;
            const text = node.textContent;

            // Réinitialiser les compteurs des niveaux inférieurs
            if (level === 1) {
              counters.h1++;
              counters.h2 = 0;
              counters.h3 = 0;
              counters.h4 = 0;
              counters.h5 = 0;
            } else if (level === 2) {
              counters.h2++;
              counters.h3 = 0;
              counters.h4 = 0;
              counters.h5 = 0;
            } else if (level === 3) {
              counters.h3++;
              counters.h4 = 0;
              counters.h5 = 0;
            } else if (level === 4) {
              counters.h4++;
              counters.h5 = 0;
            } else if (level === 5) {
              counters.h5++;
            }

            // Générer la numérotation
            let number = '';
            if (level >= 1 && counters.h1 > 0) {
              number = counters.h1.toString();
            }
            if (level >= 2 && counters.h2 > 0) {
              number += '.' + counters.h2;
            }
            if (level >= 3 && counters.h3 > 0) {
              number += '.' + counters.h3;
            }
            if (level >= 4 && counters.h4 > 0) {
              const letter = letters[counters.h4 - 1] || 'z';
              number += '.' + letter;
            }
            if (level >= 5 && counters.h5 > 0) {
              number += '.' + counters.h5;
            }

            newHeadings.push({
              id: `heading-${pos}`,
              text,
              level,
              position: pos,
              number,
            });
          }
        });

        setHeadings(newHeadings);
      } catch (error) {
        console.warn("Error updating heading numbering:", error);
        setHeadings([]);
      }
    };

    // Mettre à jour lors des changements
    const handleUpdate = () => {
      setTimeout(updateHeadingNumbering, 100);
    };

    editor.on("update", handleUpdate);
    editor.on("create", handleUpdate);

    // Mise à jour initiale
    handleUpdate();

    return () => {
      editor.off("update", handleUpdate);
      editor.off("create", handleUpdate);
    };
  }, [editor]);

  return headings;
}
