import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Routes qui nécessitent une authentification
const protectedRoutes = ['/dashboard', '/projects', '/companies', '/documents', '/settings', '/lots', '/intervenants'];

// Routes publiques (accessibles sans authentification)
const publicRoutes = ['/login', '/register'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Récupérer le token depuis les cookies (priorité aux cookies pour éviter les conflits)
  const token = request.cookies.get('auth_token')?.value;

  // Vérifier si la route est protégée
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname.startsWith(route)
  );
  
  // Vérifier si la route est publique
  const isPublicRoute = publicRoutes.some(route => 
    pathname.startsWith(route)
  );

  // Si c'est une route protégée et qu'il n'y a pas de token
  if (isProtectedRoute && !token) {
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // Si l'utilisateur est connecté et essaie d'accéder à une route publique
  if (isPublicRoute && token) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // Redirection de la racine vers le dashboard si connecté, sinon vers login
  if (pathname === '/') {
    if (token) {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    } else {
      return NextResponse.redirect(new URL('/login', request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/',
    '/login',
    '/dashboard/:path*',
    '/projects/:path*',
    '/companies/:path*',
    '/documents/:path*',
    '/settings/:path*',
    '/lots/:path*',
    '/intervenants/:path*',
  ],
};
