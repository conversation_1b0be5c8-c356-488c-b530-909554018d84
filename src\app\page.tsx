'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { Loader2 } from 'lucide-react';

export default function Home() {
  const router = useRouter();
  const { user, isLoading } = useAuth();

  useEffect(() => {
    // Attendre que l'authentification soit initialisée
    if (!isLoading) {
      // Le middleware s'occupe déjà des redirections
      // On ne fait rien ici pour éviter les conflits
    }
  }, [user, isLoading, router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="text-center">
        <div className="w-16 h-16 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4">
          <span className="text-primary-foreground font-bold text-xl">O</span>
        </div>
        <h1 className="text-2xl font-bold text-foreground mb-2">ORBIS</h1>
        <div className="flex items-center justify-center space-x-2 text-muted-foreground">
          <Loader2 className="h-4 w-4 animate-spin" />
          <p>Initialisation...</p>
        </div>
      </div>
    </div>
  );
}
