import { supabase } from './supabase'
import { api } from './api'
import type { LoginCredentials, AuthResponse, User } from './types'

class AuthService {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      console.log('🔐 AuthService.login - Début avec:', credentials.email)

      // 1. Connexion via Supabase
      console.log('📡 Appel Supabase auth...')
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      })

      if (error) {
        console.error('❌ Erreur Supabase:', error)
        throw new Error(error.message)
      }

      if (!data.session) {
        console.error('❌ Aucune session créée')
        throw new Error('Aucune session créée')
      }

      console.log('✅ Session Supabase créée')

      // 2. Récupération des infos utilisateur depuis notre API
      const token = data.session.access_token
      console.log('🎫 Token reçu:', token.substring(0, 20) + '...')

      console.log('📡 Appel API backend /v1/auth/me...')
      const userResponse = await api.get('/v1/auth/me', {
        headers: { Authorization: `Bearer ${token}` }
      })

      console.log('✅ Réponse utilisateur:', userResponse.data)

      // 3. Récupération des workspaces
      console.log('📡 Appel API backend /v1/auth/workspaces...')
      const workspacesResponse = await api.get('/v1/auth/workspaces', {
        headers: { Authorization: `Bearer ${token}` }
      })

      console.log('✅ Réponse workspaces:', workspacesResponse.data)

      const authResponse = {
        user: userResponse.data,
        token,
        workspace: workspacesResponse.data[0]?.workspace || null, // Premier workspace par défaut
        permissions: workspacesResponse.data[0]?.role ? [workspacesResponse.data[0].role] : []
      }

      console.log('✅ AuthResponse final:', authResponse)
      return authResponse
    } catch (error) {
      console.error('❌ Erreur complète de login:', error)
      throw error
    }
  }

  async logout(): Promise<void> {
    await supabase.auth.signOut()
    localStorage.removeItem('auth_token')
  }

  async getCurrentUser(): Promise<User | null> {
    const { data: { user } } = await supabase.auth.getUser()
    return user
  }

  async refreshToken(): Promise<string | null> {
    const { data, error } = await supabase.auth.refreshSession()
    if (error) return null
    return data.session?.access_token || null
  }

}

export const authService = new AuthService();
