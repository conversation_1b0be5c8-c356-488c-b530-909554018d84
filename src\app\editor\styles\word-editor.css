/* Styles Word-like avec pagination */

/* Conteneur principal avec espacement entre pages */
.word-document-container {
  /* <PERSON><PERSON><PERSON> l'effet de pages séparées */
  background-image: repeating-linear-gradient(
    transparent 0px,
    transparent 1123px,
    #f0f0f0 1123px,
    #f0f0f0 1143px /* 20px de gap */
  );
  background-size: 100% 1143px;
}

/* Conteneur principal */
.word-document-container .ProseMirror {
  outline: none !important;
  font-family: '<PERSON><PERSON>ri', 'Segoe UI', sans-serif !important;
  font-size: 11pt !important;
  line-height: 1.15 !important;
  color: #000 !important;
  min-height: calc(1123px - 192px) !important; /* Hauteur A4 moins marges */

  /* Masquer le contenu qui déborde dans les zones d'en-tête/pied */
  -webkit-mask-image: repeating-linear-gradient(
    to bottom,
    transparent 0px,
    transparent 72px, /* Zone en-tête masquée */
    black 72px, /* D<PERSON>but zone de contenu visible */
    black calc(1123px - 72px), /* Fin zone de contenu visible */
    transparent calc(1123px - 72px), /* Zone pied de page masquée */
    transparent 1123px,
    transparent 1123px, /* Gap entre pages */
    transparent 1143px
  );
  mask-image: repeating-linear-gradient(
    to bottom,
    transparent 0px,
    transparent 72px,
    black 72px,
    black calc(1123px - 72px),
    transparent calc(1123px - 72px),
    transparent 1123px,
    transparent 1123px,
    transparent 1143px
  );
  -webkit-mask-size: 100% 1143px;
  mask-size: 100% 1143px;
  -webkit-mask-repeat: repeat-y;
  mask-repeat: repeat-y;
}

/* Titres Word */
.word-document-container .ProseMirror h1 {
  font-size: 16pt !important;
  font-weight: bold !important;
  color: #2f5496 !important;
  margin: 12pt 0 6pt 0 !important;
  font-family: 'Calibri', sans-serif !important;
}

.word-document-container .ProseMirror h2 {
  font-size: 13pt !important;
  font-weight: bold !important;
  color: #2f5496 !important;
  margin: 10pt 0 6pt 0 !important;
  font-family: 'Calibri', sans-serif !important;
}

/* Paragraphes */
.word-document-container .ProseMirror p {
  margin: 0 0 8pt 0 !important;
  font-family: 'Calibri', sans-serif !important;
  font-size: 11pt !important;
  line-height: 1.15 !important;
}

/* Listes */
.word-document-container .ProseMirror ul,
.word-document-container .ProseMirror ol {
  margin: 0 0 8pt 0 !important;
  padding-left: 18pt !important;
  font-family: 'Calibri', sans-serif !important;
}

.word-document-container .ProseMirror li {
  margin: 0 0 4pt 0 !important;
}

/* Sélection de texte Word */
.word-document-container ::selection {
  background: #316ac5 !important;
  color: white !important;
}

/* Placeholder */
.word-document-container .ProseMirror p.is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}
