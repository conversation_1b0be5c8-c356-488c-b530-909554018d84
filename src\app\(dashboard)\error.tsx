'use client'

export default function Error({ error, reset }: { error: Error & { digest?: string }; reset: () => void }) {
  return (
    <div className="min-h-[50vh] flex items-center justify-center p-6">
      <div className="max-w-md text-center space-y-4">
        <h2 className="text-xl font-semibold">Une erreur est survenue</h2>
        <p className="text-sm text-muted-foreground">{error.message || 'Erreur inconnue'}</p>
        <button onClick={reset} className="px-4 py-2 rounded bg-primary text-primary-foreground">R<PERSON>sayer</button>
      </div>
    </div>
  )
}

