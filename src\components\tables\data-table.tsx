'use client'

import React from 'react'
import { useMemo } from 'react'
import {
  ColumnDef as TSColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table'
import { ColumnDef, FetchParams, Page } from './types'
import { cn } from '@/lib/utils'

export interface DataTableProps<T> {
  entity: string
  columns: ColumnDef<T>[]
  fetcher: (params: FetchParams) => Promise<Page<T>>
  onAdd?: () => void
  onEdit?: (row: T) => void
  onDelete?: (row: T) => void
  onRowClick?: (row: T) => void
  initialPageSize?: number
  visibleRows?: number
  toolbar?: React.ReactNode
}

export function DataTable<T>({ entity, columns, fetcher, onAdd, onEdit, onDelete, onRowClick, initialPageSize = 20, visibleRows = 10, toolbar }: DataTableProps<T>) {
  const [page, setPage] = React.useState(1)
  const [size, setSize] = React.useState(initialPageSize)
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [data, setData] = React.useState<Page<T> | null>(null)
  const [loading, setLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)

  const tsColumns = useMemo<TSColumnDef<T>[]>(() =>
    columns.map(c => ({
      id: c.id,
      header: c.header,
      accessorFn: c.accessor ? (row: T) => c.accessor!(row) : undefined,
      cell: c.cell ? (ctx) => c.cell!(ctx.row.original) : (ctx) => (c.accessor ? c.accessor!(ctx.row.original) : null),
      enableSorting: c.enableSorting ?? true,
      meta: { align: c.align, width: c.width },
    })), [columns])

  const table = useReactTable({
    data: data?.items ?? [],
    columns: tsColumns,
    state: { sorting },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    manualSorting: true,
  })

  const sortParam = sorting[0] ? { id: sorting[0].id, desc: sorting[0].desc } : null

  const load = React.useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const res = await fetcher({ page, size, sort: sortParam, filters: {} })
      setData(res)
    } catch (e) {
      setError(e instanceof Error ? e.message : 'Erreur de chargement')
    } finally {
      setLoading(false)
    }
  }, [page, size, sortParam, fetcher])

  React.useEffect(() => { load() }, [load])

  const count = data?.items?.length ?? 0
  const hasActions = Boolean(onEdit || onDelete)
  const displayedRows = !loading && !error ? Math.max(1, count) : 0
  const fillerCount = !loading && !error ? Math.max(0, visibleRows - displayedRows) : 0

  return (
    <div className="space-y-3">
      {(onAdd || toolbar) && (
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2">
            {onAdd && (
              <button className="px-3 py-1.5 rounded bg-primary text-primary-foreground shadow-xs hover:shadow" onClick={onAdd}>Ajouter</button>
            )}
          </div>
          <div className="flex-1" />
          <div>{toolbar}</div>
        </div>
      )}

      <div className="rounded-xl border bg-card shadow-sm">
        <div className="h-[calc(2.5rem+_44px*_var(--rows))] min-h-[calc(2.5rem+_44px*_var(--rows))] overflow-auto" style={{ ['--rows' as any]: String(visibleRows) }}>
        <table className="w-full text-sm border-separate border-spacing-0">
          <thead className="sticky top-0 z-10 bg-muted/50 backdrop-blur supports-[backdrop-filter]:bg-muted/60">
            {table.getHeaderGroups().map(hg => (
              <tr key={hg.id} className="[&>th]:border-b [&>th]:border-t [&>th]:border-muted/50">
                {hg.headers.map(header => (
                  <th key={header.id} className={cn('px-3 py-2 text-left font-medium text-foreground sticky top-0', (header.column.columnDef.meta as any)?.align === 'right' && 'text-right', (header.column.columnDef.meta as any)?.align === 'center' && 'text-center')} style={{ width: (header.column.columnDef.meta as any)?.width }}>
                    <div className="flex items-center gap-1">
                      {flexRender(header.column.columnDef.header, header.getContext())}
                      {header.column.getCanSort() && (
                        <button
                          className="ml-1 text-xs opacity-70 hover:opacity-100"
                          onClick={() => header.column.toggleSorting(header.column.getIsSorted() === 'asc')}
                          aria-label="Trier"
                        >
                          {header.column.getIsSorted() === 'desc' ? '▼' : header.column.getIsSorted() === 'asc' ? '▲' : '↕'}
                        </button>
                      )}
                    </div>
                  </th>
                ))}
                {hasActions && <th className="px-3 py-2 text-right w-14"></th>}
              </tr>
            ))}
          </thead>
          <tbody>
            {loading && (
              <tr><td colSpan={table.getAllColumns().length + (hasActions ? 1 : 0)} className="px-3 py-6 text-center text-muted-foreground">Chargement…</td></tr>
            )}
            {error && !loading && (
              <tr><td colSpan={table.getAllColumns().length + (hasActions ? 1 : 0)} className="px-3 py-6 text-center text-destructive">{error}</td></tr>
            )}
            {!loading && !error && table.getRowModel().rows.map((r) => (
              <tr key={r.id} className="border-t hover:bg-muted/30 cursor-pointer" onClick={() => onRowClick?.(r.original)}>
                {r.getVisibleCells().map(cell => (
                  <td key={cell.id} className={cn('px-3 py-2 align-middle', (cell.column.columnDef.meta as any)?.align === 'right' && 'text-right', (cell.column.columnDef.meta as any)?.align === 'center' && 'text-center')}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
                {hasActions && (
                  <td className="px-3 py-2 text-right whitespace-nowrap" onClick={(e) => e.stopPropagation()}>
                    {onEdit && <button className="px-2 py-1 text-xs rounded border mr-2" onClick={() => onEdit(r.original)}>Modifier</button>}
                    {onDelete && <button className="px-2 py-1 text-xs rounded border" onClick={() => onDelete(r.original)}>Supprimer</button>}
                  </td>
                )}
              </tr>
            ))}
            {fillerCount > 0 && Array.from({ length: fillerCount }).map((_, i) => (
              <tr key={`filler-${i}`} className="border-t">
                {table.getAllLeafColumns().map(col => (
                  <td key={col.id} className="px-3 py-2 text-transparent select-none">—</td>
                ))}
                {hasActions && <td className="px-3 py-2"> </td>}
              </tr>
            ))}
          </tbody>
        </table>
        </div>
      </div>

      <div className="flex items-center justify-end gap-2">
        <select className="border rounded px-2 py-1 text-sm" value={size} onChange={(e) => { setPage(1); setSize(Number(e.target.value)) }}>
          {[10, 20, 50].map(s => <option key={s} value={s}>{s} / page</option>)}
        </select>
        <button className="px-2 py-1 border rounded text-sm" disabled={page <= 1} onClick={() => setPage(p => p - 1)}>Précédent</button>
        <span className="text-sm text-muted-foreground">Page {page}</span>
        <button className="px-2 py-1 border rounded text-sm" disabled={(data?.items?.length || 0) < size} onClick={() => setPage(p => p + 1)}>Suivant</button>
      </div>
    </div>
  )
}

