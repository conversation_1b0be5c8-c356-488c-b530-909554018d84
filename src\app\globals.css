@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  /* ORBIS Custom Teal Palette */
  /* #F2F2F2 - Gris clair pour arrière-plans */
  --background: oklch(0.96 0 0);
  /* #014040 - Teal très foncé pour texte principal */
  --foreground: oklch(0.25 0.08 180);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.25 0.08 180);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.25 0.08 180);
  /* #1DD1A1 - Teal principal pour boutons primaires (plus foncé) */
  --primary: oklch(0.65 0.18 165);
  --primary-foreground: oklch(1 0 0);
  /* #F2F2F2 - Gris clair pour éléments secondaires */
  --secondary: oklch(0.96 0 0);
  --secondary-foreground: oklch(0.25 0.08 180);
  --muted: oklch(0.96 0 0);
  --muted-foreground: oklch(0.5 0.05 180);
  /* #03A696 - Teal clair pour accents/hover (plus foncé) */
  --accent: oklch(0.55 0.15 170);
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.9 0.02 180);
  --input: oklch(0.98 0 0);
  --ring: oklch(0.65 0.18 165);
  --chart-1: oklch(0.65 0.18 165);
  --chart-2: oklch(0.55 0.15 170);
  --chart-3: oklch(0.35 0.1 180);
  --chart-4: oklch(0.45 0.12 175);
  --chart-5: oklch(0.35 0.08 185);
  /* #025959 - Teal foncé pour sidebar (plus contrasté) */
  --sidebar: oklch(0.25 0.12 180);
  --sidebar-foreground: oklch(1 0 0);
  --sidebar-primary: oklch(0.65 0.18 165);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.35 0.1 175);
  --sidebar-accent-foreground: oklch(1 0 0);
  --sidebar-border: oklch(0.3 0.1 180);
  --sidebar-ring: oklch(0.65 0.18 165);
}

.dark {
  /* Mode sombre avec palette teal ORBIS */
  --background: oklch(0.15 0.05 180);
  --foreground: oklch(0.95 0 0);
  --card: oklch(0.2 0.06 180);
  --card-foreground: oklch(0.95 0 0);
  --popover: oklch(0.2 0.06 180);
  --popover-foreground: oklch(0.95 0 0);
  --primary: oklch(0.65 0.18 165);
  --primary-foreground: oklch(0.15 0.05 180);
  --secondary: oklch(0.25 0.08 180);
  --secondary-foreground: oklch(0.95 0 0);
  --muted: oklch(0.25 0.08 180);
  --muted-foreground: oklch(0.7 0.05 180);
  --accent: oklch(0.55 0.15 170);
  --accent-foreground: oklch(0.15 0.05 180);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(0.3 0.08 180);
  --input: oklch(0.22 0.06 180);
  --ring: oklch(0.65 0.18 165);
  --chart-1: oklch(0.65 0.18 165);
  --chart-2: oklch(0.55 0.15 170);
  --chart-3: oklch(0.45 0.12 175);
  --chart-4: oklch(0.35 0.1 180);
  --chart-5: oklch(0.25 0.08 185);
  --sidebar: oklch(0.12 0.04 180);
  --sidebar-foreground: oklch(0.95 0 0);
  --sidebar-primary: oklch(0.65 0.18 165);
  --sidebar-primary-foreground: oklch(0.12 0.04 180);
  --sidebar-accent: oklch(0.25 0.08 180);
  --sidebar-accent-foreground: oklch(0.95 0 0);
  --sidebar-border: oklch(0.3 0.08 180);
  --sidebar-ring: oklch(0.65 0.18 165);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
