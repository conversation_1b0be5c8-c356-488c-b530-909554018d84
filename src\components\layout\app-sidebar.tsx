"use client";

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarFooter,
} from '@/components/ui/sidebar';
import { LogOut } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/button';
import { navigationItems } from '@/constants/navigation';

export function AppSidebar() {
  const pathname = usePathname();
  const { logout, user } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <Sidebar className="border-r border-sidebar-border bg-sidebar">
      {/* Header avec logo ORBIS */}
      <SidebarHeader className="p-6 border-b border-sidebar-border">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-sm">O</span>
          </div>
          <div>
            <h1 className="text-lg font-semibold text-sidebar-foreground">ORBIS</h1>
            <p className="text-xs text-sidebar-foreground/70">Gestion de projets</p>
          </div>
        </div>
      </SidebarHeader>

      {/* Navigation principale */}
      <SidebarContent className="p-4">
        <SidebarMenu>
          {navigationItems.map((item) => {
            const isActive = pathname === item.href || pathname.startsWith(item.href + '/');
            const Icon = item.icon;
            
            return (
              <SidebarMenuItem key={item.href}>
                <SidebarMenuButton 
                  asChild 
                  isActive={isActive}
                  className="w-full justify-start"
                >
                  <Link href={item.href} className="flex items-center space-x-3 px-3 py-2">
                    <Icon className="h-4 w-4" />
                    <span>{item.title}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
      </SidebarContent>

      {/* Footer avec utilisateur et déconnexion */}
      <SidebarFooter className="p-4 border-t border-sidebar-border">
        <div className="space-y-3">
          {/* Info utilisateur */}
          {user && (
            <div className="px-3 py-2 rounded-lg bg-sidebar-accent/50">
              <p className="text-sm font-medium text-sidebar-foreground">
                {user.full_name || user.email}
              </p>
              <p className="text-xs text-sidebar-foreground/70">{user.email}</p>
            </div>
          )}
          
          {/* Bouton déconnexion */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleLogout}
            className="w-full justify-start text-sidebar-foreground hover:bg-sidebar-accent"
          >
            <LogOut className="h-4 w-4 mr-2" />
            Déconnexion
          </Button>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
