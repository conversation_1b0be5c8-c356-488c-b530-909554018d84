import { Loader2 } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  fullScreen?: boolean;
}

export function LoadingSpinner({ 
  size = 'md', 
  text = 'Chargement...', 
  fullScreen = false 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  const content = (
    <div className="text-center">
      <div className="w-16 h-16 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4">
        <span className="text-primary-foreground font-bold text-xl">O</span>
      </div>
      <h1 className="text-2xl font-bold text-foreground mb-2">ORBIS</h1>
      <div className="flex items-center justify-center space-x-2 text-muted-foreground">
        <Loader2 className={`${sizeClasses[size]} animate-spin`} />
        <p>{text}</p>
      </div>
    </div>
  );

  if (fullScreen) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        {content}
      </div>
    );
  }

  return content;
}
