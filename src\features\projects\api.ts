import { Page } from '@/components/tables/types'
import api from '@/lib/api'
import { MOCK_PROJECTS, MOCK_LOTS, MOCK_STAKEHOLDERS, MOCK_GDOCS, MOCK_TDOCS, Stakeholder, GDocument, TDocument } from './mocks'

export type Project = {
  id: string
  name: string
  code?: string
  description?: string | null
  status?: 'INITIAL' | 'EN_COURS' | 'EN_PAUSE' | 'TERMINE' | 'ANNULE'
  nature?: 'DEVIS' | 'AO' | 'AFFAIRE'
  start_date?: string | null
  end_date?: string | null
  budget_total?: number | null
  // Address
  address?: string | null
  city?: string | null
  postal_code?: string | null
  country?: string | null
  // Client
  client_name?: string | null
  client_contact?: string | null
  // Photo
  photo_url?: string | null
  photo_filename?: string | null
}

export type Lot = {
  id: string
  project_id: string
  name: string
}

const useBackend = !!process.env.NEXT_PUBLIC_API_URL // if defined, prefer backend

export async function listProjects(params: { page: number; size: number }): Promise<Page<Project>> {
  if (useBackend) {
    try {
      const res = await api.get(`/projects`, { params: { page: params.page, size: params.size } })
      const items: Project[] = res.data
      return { items, total: items.length, page: params.page, size: params.size }
    } catch (_) {
      // fallback to mocks
    }
  }
  const start = (params.page - 1) * params.size
  const items = MOCK_PROJECTS.slice(start, start + params.size)
  return { items, total: MOCK_PROJECTS.length, page: params.page, size: params.size }
}

export async function listLotsByProject(projectId: string, params: { page: number; size: number }): Promise<Page<Lot>> {
  const filtered = MOCK_LOTS.filter(l => l.project_id === projectId)
  const start = (params.page - 1) * params.size
  const items = filtered.slice(start, start + params.size)
  return { items, total: filtered.length, page: params.page, size: params.size }
}

export async function getProject(projectId: string): Promise<Project | null> {
  if (useBackend) {
    try {
      const res = await api.get(`/projects/${projectId}`)
      return res.data as Project
    } catch (_) {
      // fallback
    }
  }
  return MOCK_PROJECTS.find(p => p.id === projectId) || null
}

export async function updateProject(projectId: string, data: Partial<Project>): Promise<Project | null> {
  if (useBackend) {
    try {
      const res = await api.put(`/projects/${projectId}`, data)
      return res.data as Project
    } catch (e) {
      console.error('updateProject failed', e)
    }
  }
  // fallback: mutate mock
  const idx = MOCK_PROJECTS.findIndex(p => p.id === projectId)
  if (idx >= 0) {
    // @ts-ignore
    MOCK_PROJECTS[idx] = { ...MOCK_PROJECTS[idx], ...data }
    return MOCK_PROJECTS[idx]
  }
  return null
}

export async function getLot(lotId: string): Promise<Lot | null> {
  return MOCK_LOTS.find(l => l.id === lotId) || null
}

export async function listStakeholders(lotId: string, params: { page: number; size: number }): Promise<Page<Stakeholder>> {
  const filtered = MOCK_STAKEHOLDERS.filter(s => s.lot_id === lotId)
  const start = (params.page - 1) * params.size
  const items = filtered.slice(start, start + params.size)
  return { items, total: filtered.length, page: params.page, size: params.size }
}

export async function listGDocs(lotId: string, params: { page: number; size: number }): Promise<Page<GDocument>> {
  const filtered = MOCK_GDOCS.filter(d => d.lot_id === lotId)
  const start = (params.page - 1) * params.size
  const items = filtered.slice(start, start + params.size)
  return { items, total: filtered.length, page: params.page, size: params.size }
}

export async function listTDocs(lotId: string, params: { page: number; size: number }): Promise<Page<TDocument>> {
  const filtered = MOCK_TDOCS.filter(d => d.lot_id === lotId)
  const start = (params.page - 1) * params.size
  const items = filtered.slice(start, start + params.size)
  return { items, total: filtered.length, page: params.page, size: params.size }
}

