'use client'

import { createContext, useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react'
import { createPortal } from 'react-dom'
import { cn } from '@/lib/utils'

export type ToastVariant = 'default' | 'success' | 'destructive' | 'info'

export type Toast = {
  id: string
  title?: string
  description?: string
  variant?: ToastVariant
  duration?: number
}

type ToastContextType = {
  show: (t: Omit<Toast, 'id'>) => void
  success: (msg: string, desc?: string) => void
  error: (msg: string, desc?: string) => void
  info: (msg: string, desc?: string) => void
}

const ToastContext = createContext<ToastContextType | null>(null)

export function useToast() {
  const ctx = useContext(ToastContext)
  if (!ctx) throw new Error('useToast must be used within ToastProvider')
  return ctx
}

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = useState<Toast[]>([])
  const counter = useRef(0)

  const remove = useCallback((id: string) => {
    setToasts((ts) => ts.filter((t) => t.id !== id))
  }, [])

  const show = useCallback((t: Omit<Toast, 'id'>) => {
    const id = `${Date.now()}-${counter.current++}`
    const duration = t.duration ?? 4000
    const toast: Toast = { id, ...t }
    setToasts((ts) => [...ts, toast])
    if (duration > 0) setTimeout(() => remove(id), duration)
  }, [remove])

  const api = useMemo<ToastContextType>(() => ({
    show,
    success: (msg, desc) => show({ title: msg, description: desc, variant: 'success' }),
    error: (msg, desc) => show({ title: msg, description: desc, variant: 'destructive' }),
    info: (msg, desc) => show({ title: msg, description: desc, variant: 'info' }),
  }), [show])

  // Expose un hook global minimal pour intégrations sans hook (ex: QueryClient options)
  useEffect(() => {
    ;(window as unknown as { __ORBIS_TOAST__?: ToastContextType }).__ORBIS_TOAST__ = api
    return () => {
      const g = (window as unknown as { __ORBIS_TOAST__?: ToastContextType })
      if (g.__ORBIS_TOAST__ === api) g.__ORBIS_TOAST__ = undefined
    }
  }, [api])

  const [mounted, setMounted] = useState(false)
  useEffect(() => { setMounted(true) }, [])

  return (
    <ToastContext.Provider value={api}>
      {children}
      {mounted && typeof document !== 'undefined'
        ? createPortal(
            <div className="pointer-events-none fixed inset-0 z-[100] flex flex-col items-end gap-2 p-4 sm:p-6">
              <div className="ml-auto flex w-full max-w-sm flex-col gap-2">
                {toasts.map((t) => (
                  <div
                    key={t.id}
                    role="status"
                    className={cn(
                      'pointer-events-auto rounded-md border shadow-md bg-card text-card-foreground p-3 grid grid-cols-[1fr_auto] gap-x-2 gap-y-1',
                      t.variant === 'destructive' && 'border-destructive/40',
                      t.variant === 'success' && 'border-green-500/30',
                      t.variant === 'info' && 'border-primary/30'
                    )}
                  >
                    {t.title && <div className="col-span-1 font-medium">{t.title}</div>}
                    <button className="col-start-2 text-sm opacity-70 hover:opacity-100" onClick={() => remove(t.id)}>✕</button>
                    {t.description && <div className="col-span-2 text-sm text-muted-foreground">{t.description}</div>}
                  </div>
                ))}
              </div>
            </div>,
            document.body
          )
        : null}
    </ToastContext.Provider>
  )
}

