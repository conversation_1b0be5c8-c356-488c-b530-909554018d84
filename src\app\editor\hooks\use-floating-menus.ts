import { useEffect, useState, useRef } from "react";
import { Editor } from "@tiptap/react";

interface MenuState {
  visible: boolean;
  top: number;
  left: number;
}

/**
 * Compute viewport coords for current selection from/to
 */
function getSelectionCoords(view: any, at: number) {
  try {
    const rect = view.coordsAtPos(at);
    return rect; // { top,left,right,bottom }
  } catch {
    return null;
  }
}

export function useFloatingMenus(editor: Editor | null) {
  const contentRef = useRef<HTMLDivElement | null>(null);
  
  // Inline "bubble" format toolbar
  const [bubble, setBubble] = useState<MenuState>({
    visible: false,
    top: 0,
    left: 0,
  });

  // Inline floating insert menu shown at start of empty paragraph
  const [floatMenu, setFloatMenu] = useState<MenuState>({
    visible: false,
    top: 0,
    left: 0,
  });

  useEffect(() => {
    if (!editor) return;

    const updateUI = () => {
      const view = (editor as any).view;
      const sel = editor.state.selection as any;
      const container = contentRef.current;
      if (!view || !container) {
        setBubble((b) => ({ ...b, visible: false }));
        setFloatMenu((m) => ({ ...m, visible: false }));
        return;
      }

      const containerRect = container.getBoundingClientRect();

      // Bubble shows when selection is a range (not empty)
      if (!sel.empty) {
        const fromRect = getSelectionCoords(view, sel.from);
        const toRect = getSelectionCoords(view, sel.to);
        const tgt = toRect ?? fromRect;
        if (tgt) {
          const top = tgt.top - containerRect.top - 8;
          const center =
            ((fromRect?.left ?? tgt.left) + (toRect?.right ?? tgt.right)) / 2;
          const left = center - containerRect.left;
          setBubble({ visible: true, top, left });
        } else {
          setBubble((b) => ({ ...b, visible: false }));
        }
      } else {
        setBubble((b) => ({ ...b, visible: false }));
      }

      // Floating insert: at start of empty paragraph
      const $from = sel.$from;
      const isAtStart = $from.parentOffset === 0;
      const isEmptyPara =
        $from.parent?.type?.name === "paragraph" && $from.parent.content.size === 0;

      if (isAtStart && isEmptyPara) {
        const caretRect = getSelectionCoords(view, sel.from);
        if (caretRect) {
          const top = caretRect.top - containerRect.top - 2;
          const left = caretRect.left - containerRect.left - 44; // a bit on the left
          setFloatMenu({ visible: true, top, left });
        } else {
          setFloatMenu((m) => ({ ...m, visible: false }));
        }
      } else {
        setFloatMenu((m) => ({ ...m, visible: false }));
      }
    };

    updateUI();

    editor.on("selectionUpdate", updateUI);
    editor.on("update", updateUI);
    editor.on("focus", updateUI);
    editor.on("blur", () => {
      setBubble((b) => ({ ...b, visible: false }));
      setFloatMenu((m) => ({ ...m, visible: false }));
    });

    const onScroll = () => updateUI();
    const onResize = () => updateUI();

    window.addEventListener("scroll", onScroll, true);
    window.addEventListener("resize", onResize);

    return () => {
      editor.off("selectionUpdate", updateUI);
      editor.off("update", updateUI);
      editor.off("focus", updateUI);
      window.removeEventListener("scroll", onScroll, true);
      window.removeEventListener("resize", onResize);
    };
  }, [editor]);

  return {
    bubble,
    floatMenu,
    contentRef,
  };
}
