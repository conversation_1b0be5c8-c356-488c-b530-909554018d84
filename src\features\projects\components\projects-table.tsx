'use client'

import { useRouter } from 'next/navigation'
import { DataTable } from '@/components/tables/data-table'
import { projectColumns } from '@/features/projects/columns'
import { listProjects } from '@/features/projects/api'

export function ProjectsTable() {
  const router = useRouter()
  return (
    <DataTable
      entity="projects"
      columns={projectColumns}
      fetcher={({ page, size }) => listProjects({ page, size })}
      onRowClick={(row: any) => router.push(`/projects/${row.id}`)}
      visibleRows={10}
      onAdd={() => {}}
    />
  )
}

