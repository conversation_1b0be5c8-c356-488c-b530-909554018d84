import type { Project, Lot } from './api'

export const MOCK_PROJECTS: Project[] = [
  { id: 'p1', name: 'Résidence Les Jardins', status: 'EN_COURS' },
]

export const MOCK_LOTS: Lot[] = Array.from({ length: 12 }).map((_, i) => ({ id: `l${i+1}`, project_id: 'p1', name: `Lot ${i+1}` }))

export type Stakeholder = { id: string; lot_id: string; company: string; role: string; contact: string }
export type GDocument = { id: string; lot_id: string; name: string; type: string; size: string; date: string }
export type TDocument = { id: string; lot_id: string; title: string; doc_type: string; indice: string; version: string }

export const MOCK_STAKEHOLDERS: Stakeholder[] = Array.from({ length: 8 }).map((_, i) => ({
  id: `s${i+1}`, lot_id: 'l1', company: `Entreprise ${i+1}`, role: i % 2 ? 'Electricien' : 'Maçon', contact: `contact${i+1}@exemple.com`
}))

export const MOCK_GDOCS: GDocument[] = Array.from({ length: 10 }).map((_, i) => ({
  id: `g${i+1}`, lot_id: 'l1', name: `Pièce Générale ${i+1}`, type: i % 2 ? 'PDF' : 'DOCX', size: `${(i+1)*100} Ko`, date: '2025-01-01'
}))

export const MOCK_TDOCS: TDocument[] = Array.from({ length: 7 }).map((_, i) => ({
  id: `t${i+1}`, lot_id: 'l1', title: `Pièce Technique ${i+1}`, doc_type: i % 2 ? 'Plan' : 'CCTP', indice: `A${i}`, version: `v${i+1}`
}))

