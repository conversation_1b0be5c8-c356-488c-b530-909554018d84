import { Node, mergeAttributes } from "@tiptap/core";

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    coverPage: {
      setCoverPage: (attributes?: { title?: string; subtitle?: string; author?: string; date?: string }) => ReturnType;
    };
  }
}

export const CoverPage = Node.create({
  name: "coverPage",
  group: "block",
  atom: true,
  selectable: true,
  draggable: false,

  addAttributes() {
    return {
      title: {
        default: "Titre du Document",
        parseHTML: element => element.getAttribute("data-title"),
        renderHTML: attributes => ({
          "data-title": attributes.title,
        }),
      },
      subtitle: {
        default: "Sous-titre",
        parseHTML: element => element.getAttribute("data-subtitle"),
        renderHTML: attributes => ({
          "data-subtitle": attributes.subtitle,
        }),
      },
      author: {
        default: "Auteur",
        parseHTML: element => element.getAttribute("data-author"),
        renderHTML: attributes => ({
          "data-author": attributes.author,
        }),
      },
      date: {
        default: new Date().toLocaleDateString("fr-FR"),
        parseHTML: element => element.getAttribute("data-date"),
        renderHTML: attributes => ({
          "data-date": attributes.date,
        }),
      },
    };
  },

  parseHTML() {
    return [{ tag: "div[data-type='cover-page']" }];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      "div",
      mergeAttributes(HTMLAttributes, {
        "data-type": "cover-page",
        class: "cover-page",
      }),
      [
        "div",
        { class: "cover-page-content" },
        [
          "div",
          { class: "cover-page-header" },
          ["h1", { class: "cover-page-title" }, HTMLAttributes["data-title"] || "Titre du Document"],
          ["h2", { class: "cover-page-subtitle" }, HTMLAttributes["data-subtitle"] || "Sous-titre"],
        ],
        [
          "div",
          { class: "cover-page-footer" },
          ["p", { class: "cover-page-author" }, `Auteur: ${HTMLAttributes["data-author"] || "Auteur"}`],
          ["p", { class: "cover-page-date" }, `Date: ${HTMLAttributes["data-date"] || new Date().toLocaleDateString("fr-FR")}`],
        ],
      ],
    ];
  },

  addCommands() {
    return {
      setCoverPage:
        (attributes = {}) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: attributes,
          });
        },
    };
  },

  addKeyboardShortcuts() {
    return {
      "Mod-Shift-C": () => this.editor.commands.setCoverPage(),
    };
  },
});
