import React from 'react';

export function PageHeader({ children }: { children: React.ReactNode }) {
  return (
    <div className="mb-6 flex items-center justify-between gap-4">
      {children}
    </div>
  );
}

export function PageTitle({ children }: { children: React.ReactNode }) {
  return <h1 className="text-3xl font-bold text-foreground">{children}</h1>;
}

export function PageDescription({ children }: { children: React.ReactNode }) {
  return <p className="text-muted-foreground">{children}</p>;
}

export function PageActions({ children }: { children: React.ReactNode }) {
  return <div className="flex items-center gap-2">{children}</div>;
}

