import React from 'react'

export type Sort = { id: string; desc: boolean } | null
export type FilterValue = string | number | string[] | [string, string]
export type Filters = Record<string, FilterValue>

export interface Page<T> {
  items: T[]
  total: number
  page: number
  size: number
}

export interface FetchParams {
  page: number
  size: number
  sort?: Sort
  filters?: Filters
}

export interface ColumnDef<T> {
  id: string
  header: string
  accessor?: (row: T) => React.ReactNode
  cell?: (row: T) => React.ReactNode
  enableSorting?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
}

