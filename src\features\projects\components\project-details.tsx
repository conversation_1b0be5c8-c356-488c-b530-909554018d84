"use client"

import { useState, useTransition } from 'react'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { useForm } from 'react-hook-form'
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetDescription, SheetFooter } from '@/components/ui/sheet'
import type { Project } from '@/features/projects/api'
import { updateProject } from '@/features/projects/api'

export function ProjectDetails({ project }: { project: Project }) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-[280px_1fr] gap-6">
      <ProjectCard project={project} />
      <AddressCard project={project} />
    </div>
  )
}

function ProjectCard({ project }: { project: Project }) {
  const [open, setOpen] = useState(false)
  return (
    <div className="rounded-md border p-4">
      <div className="flex items-start justify-between mb-3">
        <div>
          <div className="text-sm text-muted-foreground">Projet</div>
          <div className="text-lg font-medium">{project.name} <span className="text-muted-foreground">({project.code || '—'})</span></div>
        </div>
        <Button size="sm" variant="outline" onClick={() => setOpen(true)}>Modifier</Button>
      </div>
      <div className="space-y-1 text-sm">
        <div><span className="text-muted-foreground">Statut:</span> {project.status || '—'}</div>
        <div><span className="text-muted-foreground">Nature:</span> {project.nature || '—'}</div>
        <div><span className="text-muted-foreground">Budget:</span> {project.budget_total ?? '—'}</div>
      </div>

      <EditProjectSheet open={open} onOpenChange={setOpen} project={project} />
    </div>
  )
}

function AddressCard({ project }: { project: Project }) {
  const [open, setOpen] = useState(false)
  return (
    <div className="rounded-md border p-4">
      <div className="flex items-start justify-between mb-3">
        <div>
          <div className="text-sm text-muted-foreground">Adresse du chantier</div>
          <div className="text-lg font-medium">{project.address || '—'}</div>
          <div className="text-sm text-muted-foreground">{[project.postal_code, project.city].filter(Boolean).join(' ')}{project.country ? `, ${project.country}` : ''}</div>
        </div>
        <Button size="sm" variant="outline" onClick={() => setOpen(true)}>Modifier</Button>
      </div>

      <EditAddressSheet open={open} onOpenChange={setOpen} project={project} />
    </div>
  )
}

function EditProjectSheet({ open, onOpenChange, project }: { open: boolean; onOpenChange: (v: boolean) => void; project: Project }) {
  const [isPending, startTransition] = useTransition()
  const form = useForm<Partial<Project>>({ defaultValues: { name: project.name, code: project.code || '', description: project.description || '', photo_url: project.photo_url || '' } })

  const onSubmit = (values: Partial<Project>) => {
    startTransition(async () => {
      await updateProject(project.id, values)
      onOpenChange(false)
    })
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right">
        <SheetHeader>
          <SheetTitle>Modifier le projet</SheetTitle>
          <SheetDescription>Mettez à jour les informations générales du projet.</SheetDescription>
        </SheetHeader>
        <form className="p-4 space-y-4" onSubmit={form.handleSubmit(onSubmit)}>
          <Form {...form}>
            <FormField name="name" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Nom</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField name="code" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Code</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <FormField name="photo_url" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Photo (URL)</FormLabel>
                <FormControl><Input placeholder="https://..." {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <div className="flex justify-end gap-2 pt-2">
              <Button type="button" variant="ghost" onClick={() => onOpenChange(false)}>Annuler</Button>
              <Button type="submit" disabled={isPending}>Enregistrer</Button>
            </div>
          </Form>
        </form>
      </SheetContent>
    </Sheet>
  )
}

function EditAddressSheet({ open, onOpenChange, project }: { open: boolean; onOpenChange: (v: boolean) => void; project: Project }) {
  const [isPending, startTransition] = useTransition()
  const form = useForm<Partial<Project>>({ defaultValues: { address: project.address || '', city: project.city || '', postal_code: project.postal_code || '', country: project.country || '' } })

  const onSubmit = (values: Partial<Project>) => {
    startTransition(async () => {
      await updateProject(project.id, values)
      onOpenChange(false)
    })
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right">
        <SheetHeader>
          <SheetTitle>Modifier l'adresse</SheetTitle>
          <SheetDescription>Rue, code postal, ville, pays.</SheetDescription>
        </SheetHeader>
        <form className="p-4 space-y-4" onSubmit={form.handleSubmit(onSubmit)}>
          <Form {...form}>
            <FormField name="address" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Adresse</FormLabel>
                <FormControl><Input {...field} placeholder="12 rue de la Paix" /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <div className="grid grid-cols-2 gap-3">
              <FormField name="postal_code" control={form.control} render={({ field }) => (
                <FormItem>
                  <FormLabel>Code postal</FormLabel>
                  <FormControl><Input {...field} /></FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField name="city" control={form.control} render={({ field }) => (
                <FormItem>
                  <FormLabel>Ville</FormLabel>
                  <FormControl><Input {...field} /></FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </div>
            <FormField name="country" control={form.control} render={({ field }) => (
              <FormItem>
                <FormLabel>Pays</FormLabel>
                <FormControl><Input {...field} /></FormControl>
                <FormMessage />
              </FormItem>
            )} />
            <div className="flex justify-end gap-2 pt-2">
              <Button type="button" variant="ghost" onClick={() => onOpenChange(false)}>Annuler</Button>
              <Button type="submit" disabled={isPending}>Enregistrer</Button>
            </div>
          </Form>
        </form>
      </SheetContent>
    </Sheet>
  )
}

