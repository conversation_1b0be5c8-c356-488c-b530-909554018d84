'use client'

import { PropsWithChildren, useState } from 'react'
import { QueryClient, QueryClientProvider, QueryCache, MutationCache } from '@tanstack/react-query'

export function QueryProvider({ children }: PropsWithChildren) {
  const [client] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 30_000,
            gcTime: 5 * 60_000,
            retry: 1,
            refetchOnWindowFocus: false,
          },
          mutations: {
            retry: 0,
          },
        },
        queryCache: new QueryCache({
          onError: (err) => {
            const msg = err instanceof Error ? err.message : 'Une erreur est survenue'
            const g = (window as unknown as { __ORBIS_TOAST__?: { error: (t: string, d?: string) => void } })
            g.__ORBIS_TOAST__?.error('Erreur', msg)
          },
        }),
        mutationCache: new MutationCache({
          onError: (err) => {
            const msg = err instanceof Error ? err.message : 'Une erreur est survenue'
            const g = (window as unknown as { __ORBIS_TOAST__?: { error: (t: string, d?: string) => void } })
            g.__ORBIS_TOAST__?.error('Erreur', msg)
          },
        }),
      })
  )

  return <QueryClientProvider client={client}>{children}</QueryClientProvider>
}

