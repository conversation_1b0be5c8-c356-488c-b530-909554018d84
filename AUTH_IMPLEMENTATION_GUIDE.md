# 📋 Guide Complet d'Implémentation de l'Authentification Frontend avec RBAC

## 🔐 Architecture d'authentification

### 1. **Flux d'authentification complet**

**Étapes du flux :**
1. L'utilisateur se connecte via le formulaire de login
2. Le frontend envoie les credentials à l'API backend
3. Le backend valide avec Supabase et retourne un token JWT
4. Le frontend stocke le token et récupère les infos utilisateur
5. Le système RBAC vérifie les permissions pour chaque action

### 2. **Configuration Supabase**

**Créer le fichier :** `orbis-frontend/src/lib/supabase.ts`
```typescript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
```

**Variables d'environnement :** `.env.local`
```
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
NEXT_PUBLIC_API_URL=http://localhost:8000
```

### 3. **Types TypeScript pour l'authentification**

**Fichier :** `orbis-frontend/src/lib/types.ts`
```typescript
export interface User {
  id: string
  email: string
  full_name: string
  avatar_url?: string
  is_active: boolean
  created_at: string
}

export interface WorkspaceMember {
  id: number
  user_id: string
  workspace_id: number
  role: UserRole
  created_at: string
}

export enum UserRole {
  SUPER_ADMIN = "SUPER_ADMIN",
  ADMIN = "ADMIN",
  USER = "USER",
  VIEWER = "VIEWER"
}

export interface AuthState {
  user: User | null
  token: string | null
  isLoading: boolean
  workspace: Workspace | null
  permissions: string[]
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface AuthResponse {
  user: User
  token: string
  workspace: Workspace
  permissions: string[]
}
```

### 4. **Service d'authentification**

**Fichier :** `orbis-frontend/src/lib/auth.ts`
```typescript
import { supabase } from './supabase'
import { api } from './api'
import type { LoginCredentials, AuthResponse, User } from './types'

class AuthService {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    // 1. Connexion via Supabase
    const { data, error } = await supabase.auth.signInWithPassword({
      email: credentials.email,
      password: credentials.password,
    })

    if (error) throw error

    // 2. Récupération des infos utilisateur depuis notre API
    const token = data.session.access_token
    const response = await api.get('/auth/me', {
      headers: { Authorization: `Bearer ${token}` }
    })

    return {
      user: response.data.user,
      token,
      workspace: response.data.workspace,
      permissions: response.data.permissions
    }
  }

  async logout(): Promise<void> {
    await supabase.auth.signOut()
    localStorage.removeItem('auth_token')
  }

  async getCurrentUser(): Promise<User | null> {
    const { data: { user } } = await supabase.auth.getUser()
    return user
  }

  async refreshToken(): Promise<string | null> {
    const { data, error } = await supabase.auth.refreshSession()
    if (error) return null
    return data.session?.access_token || null
  }
}

export const authService = new AuthService()
```

### 5. **Hook d'authentification React**

**Fichier :** `orbis-frontend/src/hooks/use-auth.ts`
```typescript
import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { authService } from '@/lib/auth'
import type { AuthState, LoginCredentials } from '@/lib/types'

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    token: null,
    isLoading: true,
    workspace: null,
    permissions: []
  })
  
  const router = useRouter()

  const login = useCallback(async (credentials: LoginCredentials) => {
    try {
      const response = await authService.login(credentials)
      
      setAuthState({
        user: response.user,
        token: response.token,
        isLoading: false,
        workspace: response.workspace,
        permissions: response.permissions
      })
      
      localStorage.setItem('auth_token', response.token)
      router.push('/dashboard')
      
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Erreur de connexion' 
      }
    }
  }, [router])

  const logout = useCallback(async () => {
    await authService.logout()
    setAuthState({
      user: null,
      token: null,
      isLoading: false,
      workspace: null,
      permissions: []
    })
    router.push('/login')
  }, [router])

  const hasPermission = useCallback((permission: string) => {
    return authState.permissions.includes(permission)
  }, [authState.permissions])

  const hasRole = useCallback((role: string) => {
    return authState.user?.role === role
  }, [authState.user])

  useEffect(() => {
    const initAuth = async () => {
      const token = localStorage.getItem('auth_token')
      if (token) {
        try {
          // Vérifier la validité du token
          const response = await fetch('/api/auth/me', {
            headers: { Authorization: `Bearer ${token}` }
          })
          
          if (response.ok) {
            const data = await response.json()
            setAuthState({
              user: data.user,
              token,
              isLoading: false,
              workspace: data.workspace,
              permissions: data.permissions
            })
          } else {
            localStorage.removeItem('auth_token')
          }
        } catch {
          localStorage.removeItem('auth_token')
        }
      }
      setAuthState(prev => ({ ...prev, isLoading: false }))
    }

    initAuth()
  }, [])

  return {
    ...authState,
    login,
    logout,
    hasPermission,
    hasRole
  }
}
```

### 6. **Provider d'authentification**

**Fichier :** `orbis-frontend/src/components/providers/auth-provider.tsx`
```typescript
'use client'

import { createContext, useContext } from 'react'
import { useAuth } from '@/hooks/use-auth'
import type { AuthState } from '@/lib/types'

interface AuthContextType extends ReturnType<typeof useAuth> {}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const auth = useAuth()
  
  return (
    <AuthContext.Provider value={auth}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuthContext() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuthContext must be used within AuthProvider')
  }
  return context
}
```

### 7. **Composant de protection des routes**

**Fichier :** `orbis-frontend/src/components/common/protected-route.tsx`
```typescript
'use client'

import { useAuthContext } from '@/components/providers/auth-provider'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredPermission?: string
  requiredRole?: string
  fallbackUrl?: string
}

export function ProtectedRoute({ 
  children, 
  requiredPermission, 
  requiredRole,
  fallbackUrl = '/login' 
}: ProtectedRouteProps) {
  const { user, isLoading, hasPermission, hasRole } = useAuthContext()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading) {
      if (!user) {
        router.push(fallbackUrl)
        return
      }

      if (requiredPermission && !hasPermission(requiredPermission)) {
        router.push('/unauthorized')
        return
      }

      if (requiredRole && !hasRole(requiredRole)) {
        router.push('/unauthorized')
        return
      }
    }
  }, [user, isLoading, requiredPermission, requiredRole, hasPermission, hasRole, router, fallbackUrl])

  if (isLoading) {
    return <div>Chargement...</div>
  }

  if (!user) {
    return null
  }

  if (requiredPermission && !hasPermission(requiredPermission)) {
    return null
  }

  if (requiredRole && !hasRole(requiredRole)) {
    return null
  }

  return <>{children}</>
}
```

### 8. **Formulaire de connexion**

**Fichier :** `orbis-frontend/src/app/login/page.tsx`
```typescript
'use client'

import { useState } from 'react'
import { useAuthContext } from '@/components/providers/auth-provider'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function LoginPage() {
  const { login } = useAuthContext()
  const [credentials, setCredentials] = useState({ email: '', password: '' })
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    const result = await login(credentials)
    
    if (!result.success) {
      setError(result.error || 'Erreur de connexion')
    }
    
    setIsLoading(false)
  }

  return (
    <div className="flex min-h-screen items-center justify-center">
      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle>Connexion ORBIS</CardTitle>
          <CardDescription>Entrez vos identifiants pour accéder à l'application</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={credentials.email}
                onChange={(e) => setCredentials(prev => ({ ...prev, email: e.target.value }))}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Mot de passe</Label>
              <Input
                id="password"
                type="password"
                value={credentials.password}
                onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                required
              />
            </div>
            {error && (
              <div className="text-sm text-red-500">{error}</div>
            )}
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? 'Connexion...' : 'Se connecter'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
```

### 9. **Middleware de protection**

**Fichier :** `orbis-frontend/src/middleware.ts`
```typescript
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const token = request.cookies.get('auth_token')
  
  // Routes protégées
  const protectedPaths = ['/dashboard', '/projects', '/lots']
  const isProtectedPath = protectedPaths.some(path => 
    request.nextpathname.startsWith(path)
  )
  
  if (isProtectedPath && !token) {
    return NextResponse.redirect(new URL('/login', request.url))
  }
  
  return NextResponse.next()
}

export const config = {
  matcher: ['/dashboard/:path*', '/projects/:path*', '/lots/:path*']
}
```

### 10. **Utilisation dans les composants**

**Exemple d'utilisation :**
```typescript
// Dans un composant
import { useAuthContext } from '@/components/providers/auth-provider'
import { ProtectedRoute } from '@/components/common/protected-route'

function ProjectList() {
  const { user, hasPermission } = useAuthContext()

  return (
    <ProtectedRoute requiredPermission="projects:read">
      <div>
        <h1>Projets</h1>
        {hasPermission('projects:create') && (
          <button>Créer un projet</button>
        )}
      </div>
    </ProtectedRoute>
  )
}
```

### 11. **Configuration API**

**Fichier :** `orbis-frontend/src/lib/api.ts`
```typescript
import axios from 'axios'

const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
})

// Intercepteur pour ajouter le token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Intercepteur pour gérer les erreurs d'authentification
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

export { api }
```

### 12. **Permissions RBAC disponibles**

**Permissions par rôle :**

- **SUPER_ADMIN** : Toutes les permissions
- **ADMIN** : 
  - projects:create, projects:read, projects:update, projects:delete
  - lots:create, lots:read, lots:update, lots:delete
  - users:create, users:read, users:update, users:delete
- **USER** :
  - projects:read, projects:update (ses projets)
  - lots:read, lots:update (ses lots)
- **VIEWER** :
  - projects:read
  - lots:read

### 13. **Installation des dépendances**

```bash
cd orbis-frontend
npm install @supabase/supabase-js axios
npm install --save-dev @types/node
```

### 14. **Test de l'authentification**

**Comptes de test disponibles :**
- Email: <EMAIL> (SUPER_ADMIN)
- Email: <EMAIL> (USER)

**Endpoints API à tester :**
- POST /api/v1/auth/login
- GET /api/v1/auth/me
- GET /api/v1/auth/refresh

### 15. **Gestion des erreurs**

**Erreurs courantes et solutions :**
- 401 Unauthorized : Token expiré ou invalide
- 403 Forbidden : Permissions insuffisantes
- 404 Not Found : Ressource inexistante
- 500 Internal Server Error : Erreur serveur

Cette implémentation fournit une authentification complète avec gestion des rôles et permissions via RBAC, intégration Supabase, et protection des routes côté frontend.