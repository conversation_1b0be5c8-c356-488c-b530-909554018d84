export type ApiOptions = {
  baseURL?: string
  token?: string | null
}

const DEFAULT_BASE_URL = process.env.NEXT_PUBLIC_API_URL || ''

export function createApiClient(opts: ApiOptions = {}) {
  const baseURL = opts.baseURL || DEFAULT_BASE_URL

  async function request<T>(path: string, init?: RequestInit): Promise<T> {
    const res = await fetch(`${baseURL}${path}`, {
      ...init,
      headers: {
        'Content-Type': 'application/json',
        ...(init?.headers || {}),
      },
      // Important: cookies pour auth côté navigateur si nécessaire
      credentials: 'include',
      cache: 'no-store',
    })

    if (!res.ok) {
      if (res.status === 401) {
        // TODO: déclencher une déconnexion / refresh selon stratégie
      }
      const text = await res.text()
      throw new Error(text || `HTTP ${res.status}`)
    }

    // Essayer JSO<PERSON> sinon renvoyer vide
    const contentType = res.headers.get('content-type') || ''
    if (contentType.includes('application/json')) {
      return (await res.json()) as T
    }
    return undefined as unknown as T
  }

  return {
    get: <T>(path: string) => request<T>(path),
    post: <T, B = unknown>(path: string, body?: B) => request<T>(path, { method: 'POST', body: JSON.stringify(body) }),
    patch: <T, B = unknown>(path: string, body?: B) => request<T>(path, { method: 'PATCH', body: JSON.stringify(body) }),
    del: <T>(path: string) => request<T>(path, { method: 'DELETE' }),
  }
}

export const api = createApiClient()

