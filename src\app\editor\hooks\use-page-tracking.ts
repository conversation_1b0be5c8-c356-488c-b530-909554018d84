import { useEffect, useState, useRef } from "react";
import { Editor } from "@tiptap/react";

export function usePageTracking(editor: Editor | null) {
  const [pages, setPages] = useState<number>(1);
  const contentRef = useRef<HTMLDivElement | null>(null);
  const scrollRef = useRef<HTMLDivElement | null>(null);

  // Track page count from pagination-plus
  useEffect(() => {
    if (!editor) return;

    const updatePages = () => {
      const root = contentRef.current;
      if (!root) return;

      // Chercher les pages générées par pagination-plus
      const paginationPages = root.querySelectorAll(".page, [data-page]");
      const pageCount = Math.max(1, paginationPages.length);
      setPages(pageCount);
    };

    // Délai pour laisser pagination-plus se mettre en place
    const timer = setTimeout(updatePages, 500);

    const handleUpdate = () => {
      setTimeout(updatePages, 200);
    };

    editor.on("update", handleUpdate);

    return () => {
      clearTimeout(timer);
      editor.off("update", handleUpdate);
    };
  }, [editor]);

  return {
    pages,
    contentRef,
    scrollRef,
  };
}
