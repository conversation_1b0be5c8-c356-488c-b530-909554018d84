import React, { useState } from "react";
import { Search, BookOpen } from "lucide-react";
import { HeadingDropdown } from "./heading-dropdown";

interface HeadingInfo {
  id: string;
  text: string;
  level: number;
  position: number;
  number: string;
}

interface PageSidebarProps {
  pages: number;
  onGotoPage: (pageIndex: number) => void;
  headings?: HeadingInfo[];
  onGotoHeading?: (position: number) => void;
}

export function PageSidebar({ pages, onGotoPage, headings = [], onGotoHeading }: PageSidebarProps) {
  const [pageInput, setPageInput] = useState("");

  const handlePageNavigation = (e: React.FormEvent) => {
    e.preventDefault();
    const pageNum = parseInt(pageInput);
    if (pageNum >= 1 && pageNum <= pages) {
      onGotoPage(pageNum - 1); // Convert to 0-based index
      setPageInput("");
    }
  };

  const handleHeadingClick = (heading: HeadingInfo) => {
    if (onGotoHeading) {
      onGotoHeading(heading.position);
    }
  };

  return (
    <aside className="w-80 border-r bg-secondary/50 flex flex-col overflow-hidden">
      {/* Navigation par numéro de page */}
      <div className="p-4 border-b bg-background/50">
        <div className="mb-3 text-xs font-semibold uppercase text-muted-foreground flex items-center gap-2">
          <Search className="size-3" />
          Navigation
        </div>

        <form onSubmit={handlePageNavigation} className="mb-3">
          <div className="flex gap-2">
            <input
              type="number"
              min="1"
              max={pages}
              value={pageInput}
              onChange={(e) => setPageInput(e.target.value)}
              placeholder={`Page (1-${pages})`}
              className="flex-1 px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-primary"
            />
            <button
              type="submit"
              className="px-3 py-1 text-sm bg-primary text-primary-foreground rounded hover:bg-primary/90"
            >
              Aller
            </button>
          </div>
        </form>

        <div className="text-xs text-muted-foreground text-center">
          {pages} page{pages > 1 ? "s" : ""} au total
        </div>
      </div>

      {/* Séparateur */}
      <div className="h-px bg-border"></div>

      {/* Sommaire */}
      <div className="flex-1 overflow-auto sidebar-scroll">
        <div className="p-4">
          <div className="mb-3 text-xs font-semibold uppercase text-muted-foreground flex items-center gap-2">
            <BookOpen className="size-3" />
            Navigation par titres
          </div>

          <HeadingDropdown
            headings={headings}
            onSelectHeading={handleHeadingClick}
          />

          <div className="mt-4 mb-3 text-xs font-semibold uppercase text-muted-foreground">
            Tous les titres
          </div>

          {headings.length > 0 ? (
            <div className="space-y-1">
              {headings.map((heading) => (
                <button
                  key={heading.id}
                  onClick={() => handleHeadingClick(heading)}
                  className="w-full text-left p-2 rounded hover:bg-accent/20 transition-colors group sidebar-heading-link"
                  title={`${heading.number} ${heading.text}`}
                >
                  <div
                    className="flex items-start gap-2"
                    style={{ marginLeft: `${(heading.level - 1) * 12}px` }}
                  >
                    <span className="text-xs text-blue-600 font-mono min-w-[50px] font-semibold">
                      {heading.number}
                    </span>
                    <span
                      className={`text-xs leading-relaxed group-hover:text-foreground transition-colors ${
                        heading.level === 1
                          ? 'font-semibold text-foreground'
                          : heading.level === 2
                          ? 'font-medium text-muted-foreground'
                          : 'text-muted-foreground/80'
                      }`}
                      style={{
                        fontSize: heading.level === 1 ? '11px' : heading.level === 2 ? '10px' : '9px'
                      }}
                    >
                      {heading.text}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          ) : (
            <div className="text-xs text-muted-foreground italic">
              Aucun titre détecté
            </div>
          )}
        </div>
      </div>

      {/* Pages en miniature (optionnel, en bas) */}
      <div className="border-t bg-background/50 p-3">
        <div className="mb-2 text-xs font-semibold uppercase text-muted-foreground">
          Pages rapides
        </div>
        <div className="grid grid-cols-6 gap-1">
          {Array.from({ length: Math.min(pages, 12) }).map((_, i) => (
            <button
              key={i}
              onClick={() => onGotoPage(i)}
              className="aspect-square text-xs border rounded hover:bg-accent/20 flex items-center justify-center font-medium"
              title={`Page ${i + 1}`}
            >
              {i + 1}
            </button>
          ))}
          {pages > 12 && (
            <div className="aspect-square text-xs border rounded flex items-center justify-center text-muted-foreground">
              ...
            </div>
          )}
        </div>
      </div>
    </aside>
  );
}
