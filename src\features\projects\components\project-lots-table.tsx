'use client'

import { useRouter } from 'next/navigation'
import { DataTable } from '@/components/tables/data-table'
import { lotColumns } from '@/features/projects/columns'
import { listLotsByProject } from '@/features/projects/api'

export function ProjectLotsTable({ projectId }: { projectId: string }) {
  const router = useRouter()
  return (
    <DataTable
      entity="lots"
      columns={lotColumns}
      fetcher={({ page, size }) => listLotsByProject(projectId, { page, size })}
      onRowClick={(row: any) => router.push(`/projects/${projectId}/lots/${row.id}`)}
      visibleRows={10}
      onAdd={() => {}}
    />
  )
}

