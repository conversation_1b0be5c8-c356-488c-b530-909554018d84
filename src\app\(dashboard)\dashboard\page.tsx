import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { PageHeader, PageTitle, PageDescription } from '@/components/layout/page-header';
import { PageContent } from '@/components/layout/page-content';
import Link from 'next/link';
import {
  FolderOpen,
  Building2,
  FileText,
  Clock
} from 'lucide-react';

export default function DashboardPage() {
  return (
    <PageContent>
      <PageHeader>
        <div>
          <PageTitle>Dashboard</PageTitle>
          <PageDescription>Vue d&apos;ensemble de vos projets et activités récentes</PageDescription>
        </div>
        <div>
          <Button asChild>
            <Link href="/editor/demo">Ouvrir un document (démo)</Link>
          </Button>
        </div>
      </PageHeader>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Projets Actifs</CardTitle>
            <FolderOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">+2 ce mois-ci</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Lots en Cours</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">45</div>
            <p className="text-xs text-muted-foreground">+8 cette semaine</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Entreprises</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">28</div>
            <p className="text-xs text-muted-foreground">+3 ce mois-ci</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Documents</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">156</div>
            <p className="text-xs text-muted-foreground">+12 cette semaine</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Projects */}
        <Card>
          <CardHeader>
            <CardTitle>Projets Récents</CardTitle>
            <CardDescription>Derniers projets modifiés</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { name: "Résidence Les Jardins", status: "EN_COURS", updated: "Il y a 2h" },
              { name: "Centre Commercial Atlantis", status: "INITIAL", updated: "Il y a 1j" },
              { name: "Immeuble de Bureaux Horizon", status: "TERMINE", updated: "Il y a 3j" },
            ].map((project, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium">{project.name}</p>
                  <p className="text-xs text-muted-foreground">{project.updated}</p>
                </div>
                <Badge variant={project.status === "TERMINE" ? "default" : "secondary"}>
                  {project.status}
                </Badge>
              </div>
            ))}
            <Button variant="outline" className="w-full mt-4">Voir tous les projets</Button>
          </CardContent>
        </Card>

        {/* Recent Documents */}
        <Card>
          <CardHeader>
            <CardTitle>Documents Récents</CardTitle>
            <CardDescription>Derniers documents ajoutés</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { name: "CCTP Lot Gros Œuvre", type: "CCTP", updated: "Il y a 1h" },
              { name: "Plan Architecte v2.1", type: "Plan", updated: "Il y a 4h" },
              { name: "Devis Électricité", type: "Devis", updated: "Il y a 1j" },
            ].map((doc, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="space-y-1">
                  <p className="text-sm font-medium">{doc.name}</p>
                  <p className="text-xs text-muted-foreground">{doc.updated}</p>
                </div>
                <Badge variant="outline">{doc.type}</Badge>
              </div>
            ))}
            <Button variant="outline" className="w-full mt-4">Voir tous les documents</Button>
          </CardContent>
        </Card>
      </div>
    </PageContent>
  );
}
