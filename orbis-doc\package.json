{"name": "orbis-doc", "version": "1.0.0", "description": "Module de documentation ORBIS avec pagination automatique basé sur Tiptap v3", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "demo": "npx http-server demo -p 3001", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit"}, "dependencies": {"@tiptap/core": "^3.0.9", "@tiptap/react": "^3.0.9", "@tiptap/starter-kit": "^3.0.9", "@tiptap/extension-placeholder": "^3.0.9", "@tiptap/extension-table": "^3.0.9", "@tiptap/extension-table-row": "^3.0.9", "@tiptap/extension-table-cell": "^3.0.9", "@tiptap/extension-table-header": "^3.0.9", "@tiptap/pm": "^3.0.9"}, "devDependencies": {"@types/node": "^20", "typescript": "^5", "http-server": "^14.1.1"}, "keywords": ["tiptap", "editor", "pagination", "document", "orbis", "a4", "word-like"], "author": "ORBIS Team", "license": "MIT", "files": ["dist/**/*", "src/**/*", "README.md"]}