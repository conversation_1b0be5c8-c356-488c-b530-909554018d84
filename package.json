{"name": "orbis-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@milkdown/core": "^7.15.2", "@milkdown/crepe": "^7.15.2", "@milkdown/prose": "^7.15.2", "@milkdown/react": "^7.15.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.53.0", "@tanstack/react-query": "^5.84.1", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-bubble-menu": "^3.0.9", "@tiptap/extension-floating-menu": "^3.0.9", "@tiptap/extension-placeholder": "^3.0.9", "@tiptap/pm": "^3.0.9", "@tiptap/react": "^3.0.9", "@tiptap/starter-kit": "^3.0.9", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.536.0", "next": "15.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1", "tiptap-pagination-plus": "^1.1.2", "zod": "^4.0.15", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}