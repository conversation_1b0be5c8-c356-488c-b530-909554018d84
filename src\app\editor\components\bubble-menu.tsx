import React from "react";
import { Editor } from "@tiptap/react";
import { ToolbarButton } from "./toolbar-button";
import { Bold, Italic } from "lucide-react";

interface BubbleMenuProps {
  editor: Editor;
  visible: boolean;
  top: number;
  left: number;
}

export function BubbleMenu({ editor, visible, top, left }: BubbleMenuProps) {
  if (!visible) return null;

  const isActive = (nameOrAttrs: any, attrs?: any) =>
    !!editor?.isActive(nameOrAttrs as any, attrs as any);

  return (
    <div
      className="pointer-events-auto absolute z-20 -translate-x-1/2 -translate-y-full"
      style={{ top, left }}
    >
      <div className="flex items-center gap-1 rounded-md border bg-background p-1 shadow-sm">
        <ToolbarButton
          onClick={() => editor.chain().focus().toggleBold().run()}
          active={isActive("bold")}
          title="Gras"
        >
          <Bold className="size-4" />
        </ToolbarButton>
        <ToolbarButton
          onClick={() => editor.chain().focus().toggleItalic().run()}
          active={isActive("italic")}
          title="Italique"
        >
          <Italic className="size-4" />
        </ToolbarButton>
      </div>
    </div>
  );
}
