/* Styles pour l'éditeur avec pagination et en-têtes/pieds de page */

/* Pages générées par pagination-plus */
.page {
  background: white !important;
  margin: 0 auto 20px auto !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid #d0d0d0 !important;
  width: 794px !important; /* A4 width */
  height: 1123px !important; /* A4 height */
  box-sizing: border-box !important;
  position: relative !important;
}

/* Zone de contenu avec marges Word visibles */
.page .page-content {
  position: absolute !important;
  top: 96px !important; /* 1 inch top margin */
  left: 96px !important; /* 1 inch left margin */
  right: 96px !important; /* 1 inch right margin */
  bottom: 96px !important; /* 1 inch bottom margin */
  overflow: hidden !important;
}

/* En-têtes de page - dans la marge supérieure */
.page-header {
  position: absolute !important;
  top: 24px !important; /* Dans la marge du haut */
  left: 96px !important;
  right: 96px !important;
  height: 48px !important;
  font-family: 'Calibri', sans-serif !important;
  font-size: 9pt !important;
  color: #666 !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  border-bottom: 1px solid #e0e0e0 !important;
  padding-bottom: 8px !important;
}

/* Pieds de page - dans la marge inférieure */
.page-footer {
  position: absolute !important;
  bottom: 24px !important; /* Dans la marge du bas */
  left: 96px !important;
  right: 96px !important;
  height: 48px !important;
  font-family: 'Calibri', sans-serif !important;
  font-size: 9pt !important;
  color: #666 !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  border-top: 1px solid #e0e0e0 !important;
  padding-top: 8px !important;
}

/* Marges visibles - effet Word */
.page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 96px solid transparent;
  border-color: rgba(240, 240, 240, 0.3);
  pointer-events: none;
  z-index: 1;
}

/* Styles de base pour l'éditeur */
.ProseMirror {
  outline: none !important;
  font-family: 'Calibri', 'Segoe UI', sans-serif !important;
  font-size: 11pt !important;
  line-height: 1.15 !important;
  color: #000 !important;
  position: relative !important;
  z-index: 2 !important; /* Au-dessus des marges */
  width: 100% !important;
  height: 100% !important;
}

.ProseMirror h1 {
  font-size: 16pt !important;
  font-weight: bold !important;
  color: #2f5496 !important;
  margin: 12pt 0 6pt 0 !important;
}

.ProseMirror h2 {
  font-size: 13pt !important;
  font-weight: bold !important;
  color: #2f5496 !important;
  margin: 10pt 0 6pt 0 !important;
}

.ProseMirror p {
  margin: 0 0 8pt 0 !important;
}

.ProseMirror ul, .ProseMirror ol {
  margin: 0 0 8pt 0 !important;
  padding-left: 18pt !important;
}

/* Sélection de texte */
::selection {
  background: #316ac5 !important;
  color: white !important;
}
