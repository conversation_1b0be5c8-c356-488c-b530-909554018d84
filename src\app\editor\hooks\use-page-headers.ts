import { useEffect, useRef } from 'react';

interface PageHeadersOptions {
  docId: string;
  headerLeft: string;
  headerRight: string;
  footerLeft: string;
  footerRight: string;
}

export function usePageHeaders(options: PageHeadersOptions) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    const pageHeight = 1123; // Hauteur A4
    
    // Fonction pour ajouter les en-têtes et pieds de page
    const addPageHeaders = () => {
      // Supprimer les anciens en-têtes/pieds de page
      const oldHeaders = container.querySelectorAll('.dynamic-page-header, .dynamic-page-footer');
      oldHeaders.forEach(el => el.remove());

      const contentHeight = container.scrollHeight;
      const pageCount = Math.ceil(contentHeight / pageHeight);

      for (let page = 1; page <= pageCount; page++) {
        const pageTop = (page - 1) * pageHeight;

        // Créer l'en-tête
        const header = document.createElement('div');
        header.className = 'dynamic-page-header';
        header.style.cssText = `
          position: absolute;
          top: ${pageTop + 24}px;
          left: 96px;
          right: 96px;
          height: 48px;
          font-family: 'Calibri', sans-serif;
          font-size: 9pt;
          color: #666;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #e0e0e0;
          padding-bottom: 8px;
          pointer-events: none;
          z-index: 10;
        `;
        
        header.innerHTML = `
          <span>${options.headerLeft}</span>
          <span>${options.headerRight}</span>
        `;

        // Créer le pied de page
        const footer = document.createElement('div');
        footer.className = 'dynamic-page-footer';
        footer.style.cssText = `
          position: absolute;
          top: ${pageTop + pageHeight - 72}px;
          left: 96px;
          right: 96px;
          height: 48px;
          font-family: 'Calibri', sans-serif;
          font-size: 9pt;
          color: #666;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-top: 1px solid #e0e0e0;
          padding-top: 8px;
          pointer-events: none;
          z-index: 10;
        `;
        
        footer.innerHTML = `
          <span>${options.footerLeft.replace('{page}', page.toString())}</span>
          <span>${options.footerRight}</span>
        `;

        container.appendChild(header);
        container.appendChild(footer);
      }
    };

    // Observer pour détecter les changements de contenu
    const observer = new MutationObserver(() => {
      setTimeout(addPageHeaders, 100); // Délai pour laisser le DOM se mettre à jour
    });

    observer.observe(container, {
      childList: true,
      subtree: true,
      characterData: true
    });

    // Observer pour détecter les changements de taille
    const resizeObserver = new ResizeObserver(() => {
      setTimeout(addPageHeaders, 100);
    });

    resizeObserver.observe(container);

    // Ajouter les en-têtes initiaux
    setTimeout(addPageHeaders, 100);

    return () => {
      observer.disconnect();
      resizeObserver.disconnect();
    };
  }, [options]);

  return containerRef;
}
