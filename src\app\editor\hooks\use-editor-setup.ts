import { useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";

export function useEditorSetup(docId: string) {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        horizontalRule: false,
      }),
      Placeholder.configure({
        placeholder: "Tapez du contenu ici...",
      }),
    ],
    content: `
      <h1>Document ${docId}</h1>
      <p>Ceci est un éditeur avec pagination automatique.</p>
      <p>Le contenu se répartit automatiquement sur plusieurs pages.</p>
      <h2>Fonctionnalités</h2>
      <ul>
        <li>Pagination automatique A4</li>
        <li>Formatage de texte (gras, italique)</li>
        <li>Titres H1 et H2</li>
        <li>Listes à puces et numérotées</li>
      </ul>
      <h2>Contenu de test</h2>
      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
      <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
      <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>
      <p>Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.</p>
    `,
    autofocus: "end",
    immediatelyRender: false,
    editorProps: {
      attributes: {
        class: "tiptap focus:outline-none",
      },
    },
  }, [docId]);

  return editor;
}
