// types/user.ts
export interface UserProfile {
  id: string
  auth_user_uid: string
  email: string
  full_name?: string
  avatar_url?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

// types/auth.ts
export interface AuthState {
  user: { id?: string; email?: string; full_name?: string } | null
  token: string | null
  isLoading: boolean
  workspace: { id?: string; name?: string } | null
  permissions: string[]
}

export interface LoginCredentials {
  email: string
  password: string
}

// types/api.ts
export interface ApiResponse<T> {
  data?: T
  error?: string
  success: boolean
}

// types/auth response
export interface User {
  id: string
  email?: string
  full_name?: string
  user_metadata?: {
    full_name?: string
  }
}

export interface Workspace {
  id?: string
  name?: string
}

export interface AuthResponse {
  token: string
  user: User
  workspace: Workspace | null
  permissions: string[]
}
