import React, { useState } from "react";
import { ChevronDown, Hash } from "lucide-react";

interface HeadingInfo {
  id: string;
  text: string;
  level: number;
  position: number;
  number: string;
}

interface HeadingDropdownProps {
  headings: HeadingInfo[];
  onSelectHeading: (heading: HeadingInfo) => void;
}

export function HeadingDropdown({ headings, onSelectHeading }: HeadingDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedHeading, setSelectedHeading] = useState<HeadingInfo | null>(null);

  const handleSelect = (heading: HeadingInfo) => {
    setSelectedHeading(heading);
    setIsOpen(false);
    onSelectHeading(heading);
  };

  const getIndentStyle = (level: number) => ({
    paddingLeft: `${(level - 1) * 16 + 12}px`,
  });

  const getLevelColor = (level: number) => {
    switch (level) {
      case 1: return "text-blue-600 font-semibold";
      case 2: return "text-blue-500 font-medium";
      case 3: return "text-gray-700";
      case 4: return "text-gray-600";
      case 5: return "text-gray-500";
      default: return "text-gray-500";
    }
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between px-3 py-2 text-sm border rounded-md bg-background hover:bg-accent/20 transition-colors"
      >
        <div className="flex items-center gap-2">
          <Hash className="size-4 text-muted-foreground" />
          <span className="truncate">
            {selectedHeading 
              ? `${selectedHeading.number} ${selectedHeading.text}`
              : "Sélectionner un titre..."
            }
          </span>
        </div>
        <ChevronDown 
          className={`size-4 text-muted-foreground transition-transform ${
            isOpen ? "rotate-180" : ""
          }`} 
        />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-background border rounded-md shadow-lg z-50 max-h-80 overflow-auto">
          {headings.length > 0 ? (
            <div className="py-1">
              {headings.map((heading) => (
                <button
                  key={heading.id}
                  onClick={() => handleSelect(heading)}
                  className="w-full text-left px-3 py-2 hover:bg-accent/20 transition-colors"
                  style={getIndentStyle(heading.level)}
                >
                  <div className="flex items-start gap-2">
                    <span className={`text-xs font-mono min-w-[60px] ${getLevelColor(heading.level)}`}>
                      {heading.number}
                    </span>
                    <span className={`text-sm truncate ${getLevelColor(heading.level)}`}>
                      {heading.text}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          ) : (
            <div className="px-3 py-4 text-sm text-muted-foreground text-center">
              Aucun titre trouvé
            </div>
          )}
        </div>
      )}

      {/* Overlay pour fermer le dropdown */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
